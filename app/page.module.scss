@use '../src/styles/variables' as *;
@use '../src/styles/mixins' as *;

// Hero Section
.hero {
  background: linear-gradient(135deg, $primary-blue 0%, $primary-blue-dark 100%);
  color: white;
  padding: $spacing-20 0;
  
  @include mobile-only {
    padding: $spacing-16 0;
  }
}

.heroContainer {
  @include container;
  @include flexbox(space-between, center);
  gap: $spacing-12;
  
  @include mobile-only {
    flex-direction: column;
    text-align: center;
    gap: $spacing-8;
  }
}

.heroContent {
  flex: 1;
  max-width: 600px;
}

.heroTitle {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: $spacing-6;
}

.highlight {
  color: $primary-yellow;
}

.heroDescription {
  font-size: $font-size-3;
  line-height: 1.6;
  margin-bottom: $spacing-8;
  opacity: 0.9;
}

.heroActions {
  @include flexbox(flex-start, center);
  gap: $spacing-4;
  
  @include mobile-only {
    flex-direction: column;
    width: 100%;
    
    > * {
      width: 100%;
      max-width: 300px;
    }
  }
}

.heroImage {
  flex: 1;
  @include flexbox(center, center);
  max-width: 500px;
}

.placeholder {
  font-size: 8rem;
  opacity: 0.8;
  
  @include mobile-only {
    font-size: 6rem;
  }
}

// Features Section
.features {
  padding: $spacing-20 0;
  background: white;
  
  @include mobile-only {
    padding: $spacing-16 0;
  }
}

.container {
  @include container;
}

.sectionTitle {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  text-align: center;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-12;
}

.featureGrid {
  @include grid-responsive(1, 1, 3, 3);
  gap: $spacing-8;
}

.feature {
  text-align: center;
  padding: $spacing-6;
}

.featureIcon {
  @include flexbox(center, center);
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, $primary-blue, $primary-blue-light);
  color: white;
  border-radius: $border-radius-full;
  margin: 0 auto $spacing-6;
}

.featureTitle {
  font-size: $font-size-4;
  font-weight: 600;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-3;
}

.featureDescription {
  color: $primary-lighter-text-color;
  line-height: 1.6;
}

// CTA Section
.cta {
  background: $gray-100;
  padding: $spacing-20 0;
  
  @include mobile-only {
    padding: $spacing-16 0;
  }
}

.ctaContent {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-4;
}

.ctaDescription {
  font-size: $font-size-3;
  color: $primary-lighter-text-color;
  line-height: 1.6;
  margin-bottom: $spacing-8;
}
