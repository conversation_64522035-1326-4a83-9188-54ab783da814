import type { Metadata } from "next";
import "./globals.scss";
import { Providers } from "../src/components/providers/Providers";

export const metadata: Metadata = {
  title: "Picky Store - Modern E-commerce Experience",
  description: "Discover amazing products with our modern, fast, and user-friendly e-commerce platform.",
  keywords: "e-commerce, shopping, products, online store, retail",
  authors: [{ name: "Picky Store Team" }],
  creator: "Picky Store",
  publisher: "Picky Store",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Picky Store - Modern E-commerce Experience',
    description: 'Discover amazing products with our modern, fast, and user-friendly e-commerce platform.',
    siteName: 'Picky Store',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Picky Store - Modern E-commerce Experience',
    description: 'Discover amazing products with our modern, fast, and user-friendly e-commerce platform.',
    creator: '@pickystore',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
