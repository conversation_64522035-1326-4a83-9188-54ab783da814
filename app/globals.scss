@use '../src/styles/variables' as vars;
@use '../src/styles/mixins' as mixins;
@use '../src/styles/responsive';
@use '../src/styles/accessibility';
@use './reset.scss';

/* CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  font-family: vars.$primary-font-family;
  color: vars.$primary-dark-text-color;
  background-color: white;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

/* Remove default list styles */
ul,
ol {
  list-style: none;
}

/* Remove default input styles */
input,
textarea,
select {
  font: inherit;
  color: inherit;
}

/* Image defaults */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid vars.$primary-blue;
  outline-offset: 2px;
}

/* Utility classes */
.container {
  @include mixins.container;
}

.sr-only {
  @include mixins.visually-hidden;
}

.text-truncate {
  @include mixins.text-truncate;
}

/* Loading animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Utility animations */
.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideInUp {
  animation: slideInUp 0.3s ease-out;
}

.animate-slideInDown {
  animation: slideInDown 0.3s ease-out;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: vars.$gray-100;
}

::-webkit-scrollbar-thumb {
  background: vars.$gray-400;
  border-radius: vars.$border-radius-full;
}

::-webkit-scrollbar-thumb:hover {
  background: vars.$gray-500;
}