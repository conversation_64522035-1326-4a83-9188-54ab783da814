// UI state management store for global UI components
// Handles navigation, modals, notifications, and theme

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface Modal {
  id: string;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closable?: boolean;
  onClose?: () => void;
}

interface UIState {
  // Navigation state
  mobileMenuOpen: boolean;
  searchOpen: boolean;
  cartSidebarOpen: boolean;
  
  // Loading states
  globalLoading: boolean;
  pageLoading: boolean;
  
  // Notifications
  notifications: Notification[];
  
  // Modals
  modals: Modal[];
  
  // Theme
  theme: 'light' | 'dark';
  
  // Search
  searchQuery: string;
  searchHistory: string[];
  
  // Actions
  toggleMobileMenu: () => void;
  setMobileMenuOpen: (open: boolean) => void;
  toggleSearch: () => void;
  setSearchOpen: (open: boolean) => void;
  toggleCartSidebar: () => void;
  setCartSidebarOpen: (open: boolean) => void;
  
  setGlobalLoading: (loading: boolean) => void;
  setPageLoading: (loading: boolean) => void;
  
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  openModal: (modal: Omit<Modal, 'id'>) => void;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
  
  setTheme: (theme: 'light' | 'dark') => void;
  toggleTheme: () => void;
  
  setSearchQuery: (query: string) => void;
  addToSearchHistory: (query: string) => void;
  clearSearchHistory: () => void;
}

const generateId = () => Math.random().toString(36).substr(2, 9);

export const useUIStore = create<UIState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        mobileMenuOpen: false,
        searchOpen: false,
        cartSidebarOpen: false,
        globalLoading: false,
        pageLoading: false,
        notifications: [],
        modals: [],
        theme: 'light',
        searchQuery: '',
        searchHistory: [],
        
        // Navigation actions
        toggleMobileMenu: () => set((state) => ({ 
          mobileMenuOpen: !state.mobileMenuOpen 
        })),
        
        setMobileMenuOpen: (open) => set({ mobileMenuOpen: open }),
        
        toggleSearch: () => set((state) => ({ 
          searchOpen: !state.searchOpen 
        })),
        
        setSearchOpen: (open) => set({ searchOpen: open }),
        
        toggleCartSidebar: () => set((state) => ({ 
          cartSidebarOpen: !state.cartSidebarOpen 
        })),
        
        setCartSidebarOpen: (open) => set({ cartSidebarOpen: open }),
        
        // Loading actions
        setGlobalLoading: (loading) => set({ globalLoading: loading }),
        
        setPageLoading: (loading) => set({ pageLoading: loading }),
        
        // Notification actions
        addNotification: (notification) => {
          const id = generateId();
          const newNotification: Notification = {
            id,
            duration: 5000, // Default 5 seconds
            ...notification,
          };
          
          set((state) => ({
            notifications: [...state.notifications, newNotification]
          }));
          
          // Auto-remove notification after duration
          if (newNotification.duration && newNotification.duration > 0) {
            setTimeout(() => {
              get().removeNotification(id);
            }, newNotification.duration);
          }
        },
        
        removeNotification: (id) => set((state) => ({
          notifications: state.notifications.filter(n => n.id !== id)
        })),
        
        clearNotifications: () => set({ notifications: [] }),
        
        // Modal actions
        openModal: (modal) => {
          const id = generateId();
          const newModal: Modal = {
            id,
            size: 'md',
            closable: true,
            ...modal,
          };
          
          set((state) => ({
            modals: [...state.modals, newModal]
          }));
        },
        
        closeModal: (id) => {
          const modal = get().modals.find(m => m.id === id);
          if (modal?.onClose) {
            modal.onClose();
          }
          
          set((state) => ({
            modals: state.modals.filter(m => m.id !== id)
          }));
        },
        
        closeAllModals: () => {
          const { modals } = get();
          modals.forEach(modal => {
            if (modal.onClose) {
              modal.onClose();
            }
          });
          
          set({ modals: [] });
        },
        
        // Theme actions
        setTheme: (theme) => set({ theme }),
        
        toggleTheme: () => set((state) => ({
          theme: state.theme === 'light' ? 'dark' : 'light'
        })),
        
        // Search actions
        setSearchQuery: (query) => set({ searchQuery: query }),
        
        addToSearchHistory: (query) => {
          if (!query.trim()) return;
          
          set((state) => {
            const history = state.searchHistory.filter(item => item !== query);
            return {
              searchHistory: [query, ...history].slice(0, 10) // Keep last 10 searches
            };
          });
        },
        
        clearSearchHistory: () => set({ searchHistory: [] }),
      }),
      {
        name: 'ui-store',
        partialize: (state) => ({
          theme: state.theme,
          searchHistory: state.searchHistory,
          // Don't persist temporary UI states
        }),
        version: 1,
      }
    ),
    {
      name: 'UIStore',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

// Selector hooks for better performance
export const useMobileMenuOpen = () => useUIStore((state) => state.mobileMenuOpen);
export const useSearchOpen = () => useUIStore((state) => state.searchOpen);
export const useCartSidebarOpen = () => useUIStore((state) => state.cartSidebarOpen);
export const useGlobalLoading = () => useUIStore((state) => state.globalLoading);
export const usePageLoading = () => useUIStore((state) => state.pageLoading);
export const useNotifications = () => useUIStore((state) => state.notifications);
export const useModals = () => useUIStore((state) => state.modals);
export const useTheme = () => useUIStore((state) => state.theme);
export const useSearchQuery = () => useUIStore((state) => state.searchQuery);
export const useSearchHistory = () => useUIStore((state) => state.searchHistory);

// UI actions
export const useUIActions = () => useUIStore((state) => ({
  toggleMobileMenu: state.toggleMobileMenu,
  setMobileMenuOpen: state.setMobileMenuOpen,
  toggleSearch: state.toggleSearch,
  setSearchOpen: state.setSearchOpen,
  toggleCartSidebar: state.toggleCartSidebar,
  setCartSidebarOpen: state.setCartSidebarOpen,
  setGlobalLoading: state.setGlobalLoading,
  setPageLoading: state.setPageLoading,
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  clearNotifications: state.clearNotifications,
  openModal: state.openModal,
  closeModal: state.closeModal,
  closeAllModals: state.closeAllModals,
  setTheme: state.setTheme,
  toggleTheme: state.toggleTheme,
  setSearchQuery: state.setSearchQuery,
  addToSearchHistory: state.addToSearchHistory,
  clearSearchHistory: state.clearSearchHistory,
}));

// Helper hooks
export const useNotificationCount = () => useUIStore((state) => state.notifications.length);
export const useModalCount = () => useUIStore((state) => state.modals.length);
export const useHasModals = () => useUIStore((state) => state.modals.length > 0);
export const useIsLoading = () => useUIStore((state) => state.globalLoading || state.pageLoading);
