@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.button {
  @include flexbox(center, center);
  @include focus-visible;
  
  position: relative;
  border: 1px solid transparent;
  border-radius: $border-radius-md;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all $transition-fast;
  white-space: nowrap;
  user-select: none;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }
  
  &:not(:disabled):hover {
    transform: translateY(-1px);
  }
  
  &:not(:disabled):active {
    transform: translateY(0);
  }
}

// Sizes
.sm {
  padding: $spacing-2 $spacing-3;
  font-size: $font-size-1;
  gap: $spacing-1;
}

.md {
  padding: $spacing-3 $spacing-4;
  font-size: $font-size-2;
  gap: $spacing-2;
}

.lg {
  padding: $spacing-4 $spacing-6;
  font-size: $font-size-3;
  gap: $spacing-2;
}

// Variants
.primary {
  @include btn-primary;
}

.secondary {
  @include btn-secondary;
}

.success {
  @include btn-success;
}

.danger {
  @include btn-danger;
}

.warning {
  @include btn-warning;
}

.ghost {
  color: $primary-dark-text-color;
  background-color: transparent;
  border-color: transparent;
  
  &:hover {
    background-color: $gray-100;
  }
}

.link {
  color: $primary-blue;
  background-color: transparent;
  border-color: transparent;
  padding: 0;
  
  &:hover {
    color: $primary-blue-dark;
    text-decoration: underline;
    transform: none;
  }
}

// Modifiers
.fullWidth {
  width: 100%;
}

.loading {
  cursor: wait;
  
  .content {
    opacity: 0.7;
  }
}

// Icon styles
.leftIcon,
.rightIcon {
  @include flexbox(center, center);
  flex-shrink: 0;
}

.spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.content {
  @include flexbox(center, center);
  gap: inherit;
}
