.userMenu {
  position: relative;
}

.trigger {
  @include flexbox(flex-start, center);
  gap: $spacing-2;
  padding: $spacing-2 $spacing-3;
  
  @include mobile-only {
    .userName {
      display: none;
    }
  }
}

.avatar,
.avatarLarge {
  @include flexbox(center, center);
  background-color: $primary-blue;
  color: white;
  border-radius: $border-radius-full;
  font-weight: 600;
  font-size: $font-size-1;
}

.avatar {
  width: 28px;
  height: 28px;
}

.avatarLarge {
  width: 40px;
  height: 40px;
  font-size: $font-size-2;
}

.userName {
  font-weight: 500;
  color: $primary-dark-text-color;
}

.chevron {
  transition: transform $transition-fast;
  color: $gray-500;
  
  &.open {
    transform: rotate(180deg);
  }
}

.dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: $spacing-2;
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-lg;
  border: 1px solid $gray-200;
  min-width: 250px;
  z-index: $z-dropdown;
  overflow: hidden;
  
  @include mobile-only {
    right: -$spacing-4;
    min-width: 280px;
  }
}

.userInfo {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
  padding: $spacing-4;
  background-color: $gray-50;
}

.userDetails {
  flex: 1;
  min-width: 0;
}

.name {
  font-weight: 600;
  color: $primary-dark-text-color;
  @include text-truncate;
}

.email {
  font-size: $font-size-1;
  color: $gray-600;
  @include text-truncate;
}

.divider {
  height: 1px;
  background-color: $gray-200;
}

.menu {
  padding: $spacing-2 0;
}

.menuItem {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
  width: 100%;
  padding: $spacing-3 $spacing-4;
  color: $primary-dark-text-color;
  text-decoration: none;
  transition: background-color $transition-fast;
  
  &:hover {
    background-color: $gray-50;
  }
  
  span {
    font-size: $font-size-2;
  }
}

.logoutButton {
  @include flexbox(flex-start, center);
  gap: $spacing-3;
  width: 100%;
  padding: $spacing-3 $spacing-4;
  background: none;
  border: none;
  color: $error;
  cursor: pointer;
  transition: background-color $transition-fast;
  
  &:hover {
    background-color: $error-bg;
  }
  
  span {
    font-size: $font-size-2;
  }
}
