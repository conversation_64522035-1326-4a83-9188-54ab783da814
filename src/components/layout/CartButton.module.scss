.cartButton {
  position: relative;
}

.link {
  text-decoration: none;
  color: inherit;
}

.button {
  @include flexbox(center, center);
  gap: $spacing-2;
  padding: $spacing-2 $spacing-3;
  
  @include mobile-only {
    .label {
      display: none;
    }
  }
}

.iconContainer {
  position: relative;
  @include flexbox(center, center);
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: $primary-red;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: $border-radius-full;
  min-width: 18px;
  height: 18px;
  @include flexbox(center, center);
  line-height: 1;
}

.label {
  font-weight: 500;
  color: $primary-dark-text-color;
}
