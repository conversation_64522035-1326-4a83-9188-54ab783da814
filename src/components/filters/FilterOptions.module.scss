@import '../../scss/variables';
@import '../../scss/mixins';


.filter_options {
  padding: 1rem;
  background-color: $sky-lighter-blue;

  .primary_filters,
  .attribute_filters {
    div {
      h3 {
        font-weight: bold;
        text-transform: capitalize;
      }

      div {
        @include flexbox(flex-start, center);
        column-gap: .2rem;
        text-transform: capitalize;

        input {
          &:hover {
            color: $primary-blue;
          }
        }

        label {
          color: $primary-dark-text-color;

          &:hover {
            color: $primary-blue;
          }
        }
      }
    }
  }

  .primary_filters {
    div {
      .price_range_sliders {
        @include flexbox(space-between, flex-start, column);
        max-width: 60%;

        div {
          color: $primary-dark-text-color;
          width: 100%;
          @include flexbox(space-between, flex-start);
        }
      }
    }

    div:first-child {
      h3 {
        span {
          font-weight: normal;
          color: $primary-dark-text-color;
        }
      }
    }
  }

  // .attribute_filters {}
}