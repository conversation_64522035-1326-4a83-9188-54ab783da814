@import './variables';

// Generic flexbox mixin
@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {
  display: flex;
  justify-content: $justify;
  align-items: $align;
  flex-direction: $direction;
  flex-wrap: $wrap;
}

// Button mixin
@mixin btn($color, $bg-color, $border-color: transparent) {
  @include flexbox(center, center);
  color: $color;
  background-color: $bg-color;
  border: 1px solid $border-color;
  padding: $spacing-3 $spacing-4;
  border-radius: $border-radius-md;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition-fast;
  text-decoration: none;
  display: inline-flex;

  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

// Button variants
@mixin btn-primary {
  @include btn(white, $primary-blue, $primary-blue);

  &:hover {
    background-color: $primary-blue-dark;
    border-color: $primary-blue-dark;
  }
}

@mixin btn-secondary {
  @include btn($primary-blue, transparent, $primary-blue);

  &:hover {
    background-color: $primary-blue;
    color: white;
  }
}

@mixin btn-success {
  @include btn(white, $success, $success);
}

@mixin btn-danger {
  @include btn(white, $error, $error);
}

@mixin btn-warning {
  @include btn($warning-text, $warning, $warning);
}

// Input mixin
@mixin input-base {
  width: 100%;
  padding: $spacing-3 $spacing-4;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  font-size: $font-size-2;
  transition: all $transition-fast;

  &:focus {
    outline: none;
    border-color: $primary-blue;
    box-shadow: $box-shadow-blue-1;
  }

  &:disabled {
    background-color: $gray-100;
    cursor: not-allowed;
  }

  &.error {
    border-color: $error;

    &:focus {
      box-shadow: 0 0 0 2px rgba($error, 0.2);
    }
  }
}

// Card mixin
@mixin card {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-sm;
  border: 1px solid $gray-200;
  overflow: hidden;

  &:hover {
    box-shadow: $box-shadow-md;
  }
}

// Container mixin
@mixin container($max-width: $container-xl) {
  width: 100%;
  max-width: $max-width;
  margin: 0 auto;
  padding: 0 $spacing-4;

  @media (min-width: $tablet) {
    padding: 0 $spacing-6;
  }

  @media (min-width: $laptop) {
    padding: 0 $spacing-8;
  }
}

// Media query mixins (Mobile-first)
@mixin mobile-only {
  @media (max-width: #{$mobile - 1px}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: $tablet) {
    @content;
  }
}

@mixin laptop-up {
  @media (min-width: $laptop) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: $desktop) {
    @content;
  }
}

@mixin wide-up {
  @media (min-width: $wide) {
    @content;
  }
}

// Grid mixins
@mixin grid($columns: 1, $gap: $spacing-4) {
  display: grid;
  grid-template-columns: repeat($columns, 1fr);
  gap: $gap;
}

@mixin grid-responsive($mobile: 1, $tablet: 2, $laptop: 3, $desktop: 4) {
  @include grid($mobile);

  @include tablet-up {
    grid-template-columns: repeat($tablet, 1fr);
  }

  @include laptop-up {
    grid-template-columns: repeat($laptop, 1fr);
  }

  @include desktop-up {
    grid-template-columns: repeat($desktop, 1fr);
  }
}

// Text utilities
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// Loading state mixin
@mixin loading-skeleton {
  background: linear-gradient(90deg, $gray-200 25%, $gray-100 50%, $gray-200 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

// Focus visible mixin for accessibility
@mixin focus-visible {
  &:focus-visible {
    outline: 2px solid $primary-blue;
    outline-offset: 2px;
  }
}

// Visually hidden mixin for screen readers
@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}