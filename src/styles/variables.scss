// Fonts
$primary-font-family: '<PERSON>', '<PERSON> MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif;

// Font Size
$font-size-1: 12px;
$font-size-2: 16px;
$font-size-3: 18px;
$font-size-4: 20px;
$font-size-5: 24px;

// Primary Colors (Based on #0091cf)
$primary-blue: #0091cf;
$primary-blue-light: #00b3ff;
$primary-blue-dark: #006ba3;
$sky-light-blue: #a4e4ff;
$sky-lighter-blue: #d4f4ff;

// Secondary Colors
$primary-dark: #131921;
$primary-dark-blue: #232F3E;
$primary-yellow: #FFD814;
$lighten-yellow: #ffe180;
$primary-red: #cf0707;
$primary-green: #2E9F1C;

// Neutral Colors
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Text Colors
$primary-dark-text-color: #333;
$primary-lighter-text-color: #666;

// Status Colors
$success: #10b981;
$success-bg: #9fffa3;
$success-text: #002e02;

$warning: #f59e0b;
$warning-bg: #ffe180;
$warning-text: #534000;

$error: #ef4444;
$error-red: #f00000;
$error-red-bg: #FF9494;
$error-bg: #ffb2b9;
$error-text: #580007;

$info: #3b82f6;
$info-bg: #bedeff;
$info-text: #084298;

// Box shadow
$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;
$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;
$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;
$box-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
$box-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
$box-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

// Border radius
$border-radius-1: 3px;
$border-radius-2: 5px;
$border-radius-3: 8px;
$border-radius-4: 10px;
$border-radius-sm: 4px;
$border-radius-md: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;
$border-radius-full: 9999px;

// Spacing
$spacing-1: 4px;
$spacing-2: 8px;
$spacing-3: 12px;
$spacing-4: 16px;
$spacing-5: 20px;
$spacing-6: 24px;
$spacing-8: 32px;
$spacing-10: 40px;
$spacing-12: 48px;
$spacing-16: 64px;
$spacing-20: 80px;

// Padding (legacy support)
$padding-1: 5px;
$padding-2: 10px;
$padding-3: 12px;
$padding-4: 15px;
$padding-5: 20px;

// Media queries (Mobile-first approach)
$mobile: 576px;
$tablet: 768px;
$laptop: 992px;
$desktop: 1200px;
$wide: 1400px;

// Z-index layers
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;
$z-toast: 1080;

// Transitions
$transition-fast: 150ms ease-in-out;
$transition-normal: 250ms ease-in-out;
$transition-slow: 350ms ease-in-out;

// Container max widths
$container-sm: 640px;
$container-md: 768px;
$container-lg: 1024px;
$container-xl: 1280px;
$container-2xl: 1536px;
