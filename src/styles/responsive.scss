// Responsive utilities and helper classes
// Provides utility classes for responsive design

@use 'variables' as *;
@use 'mixins' as *;

// Container utilities
.container-fluid {
  width: 100%;
  padding-left: $spacing-4;
  padding-right: $spacing-4;

  @include tablet-up {
    padding-left: $spacing-6;
    padding-right: $spacing-6;
  }
}

.container-sm {
  @include container;
  max-width: 640px;
}

.container-md {
  @include container;
  max-width: 768px;
}

.container-lg {
  @include container;
  max-width: 1024px;
}

.container-xl {
  @include container;
  max-width: 1280px;
}

// Display utilities
.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.d-flex {
  display: flex !important;
}

.d-grid {
  display: grid !important;
}

// Responsive display utilities
@include mobile-only {
  .d-mobile-none {
    display: none !important;
  }
  
  .d-mobile-block {
    display: block !important;
  }
  
  .d-mobile-flex {
    display: flex !important;
  }
}

@include tablet-up {
  .d-tablet-none {
    display: none !important;
  }
  
  .d-tablet-block {
    display: block !important;
  }
  
  .d-tablet-flex {
    display: flex !important;
  }
}

@include desktop-up {
  .d-desktop-none {
    display: none !important;
  }
  
  .d-desktop-block {
    display: block !important;
  }
  
  .d-desktop-flex {
    display: flex !important;
  }
}

// Flexbox utilities
.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.justify-start {
  justify-content: flex-start !important;
}

.justify-center {
  justify-content: center !important;
}

.justify-end {
  justify-content: flex-end !important;
}

.justify-between {
  justify-content: space-between !important;
}

.justify-around {
  justify-content: space-around !important;
}

.align-start {
  align-items: flex-start !important;
}

.align-center {
  align-items: center !important;
}

.align-end {
  align-items: flex-end !important;
}

.align-stretch {
  align-items: stretch !important;
}

// Grid utilities
.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr) !important;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr) !important;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr) !important;
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr) !important;
}

.grid-cols-auto {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
}

// Gap utilities
.gap-1 {
  gap: $spacing-1 !important;
}

.gap-2 {
  gap: $spacing-2 !important;
}

.gap-3 {
  gap: $spacing-3 !important;
}

.gap-4 {
  gap: $spacing-4 !important;
}

.gap-6 {
  gap: $spacing-6 !important;
}

.gap-8 {
  gap: $spacing-8 !important;
}

// Spacing utilities
.m-0 { margin: 0 !important; }
.m-1 { margin: $spacing-1 !important; }
.m-2 { margin: $spacing-2 !important; }
.m-3 { margin: $spacing-3 !important; }
.m-4 { margin: $spacing-4 !important; }
.m-6 { margin: $spacing-6 !important; }
.m-8 { margin: $spacing-8 !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: $spacing-1 !important; }
.mt-2 { margin-top: $spacing-2 !important; }
.mt-3 { margin-top: $spacing-3 !important; }
.mt-4 { margin-top: $spacing-4 !important; }
.mt-6 { margin-top: $spacing-6 !important; }
.mt-8 { margin-top: $spacing-8 !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: $spacing-1 !important; }
.mb-2 { margin-bottom: $spacing-2 !important; }
.mb-3 { margin-bottom: $spacing-3 !important; }
.mb-4 { margin-bottom: $spacing-4 !important; }
.mb-6 { margin-bottom: $spacing-6 !important; }
.mb-8 { margin-bottom: $spacing-8 !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: $spacing-1 !important; }
.ml-2 { margin-left: $spacing-2 !important; }
.ml-3 { margin-left: $spacing-3 !important; }
.ml-4 { margin-left: $spacing-4 !important; }
.ml-auto { margin-left: auto !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: $spacing-1 !important; }
.mr-2 { margin-right: $spacing-2 !important; }
.mr-3 { margin-right: $spacing-3 !important; }
.mr-4 { margin-right: $spacing-4 !important; }
.mr-auto { margin-right: auto !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: $spacing-1 !important; }
.p-2 { padding: $spacing-2 !important; }
.p-3 { padding: $spacing-3 !important; }
.p-4 { padding: $spacing-4 !important; }
.p-6 { padding: $spacing-6 !important; }
.p-8 { padding: $spacing-8 !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: $spacing-1 !important; }
.pt-2 { padding-top: $spacing-2 !important; }
.pt-3 { padding-top: $spacing-3 !important; }
.pt-4 { padding-top: $spacing-4 !important; }
.pt-6 { padding-top: $spacing-6 !important; }
.pt-8 { padding-top: $spacing-8 !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: $spacing-1 !important; }
.pb-2 { padding-bottom: $spacing-2 !important; }
.pb-3 { padding-bottom: $spacing-3 !important; }
.pb-4 { padding-bottom: $spacing-4 !important; }
.pb-6 { padding-bottom: $spacing-6 !important; }
.pb-8 { padding-bottom: $spacing-8 !important; }

// Text utilities
.text-left {
  text-align: left !important;
}

.text-center {
  text-align: center !important;
}

.text-right {
  text-align: right !important;
}

.text-sm {
  font-size: $font-size-1 !important;
}

.text-base {
  font-size: $font-size-2 !important;
}

.text-lg {
  font-size: $font-size-3 !important;
}

.text-xl {
  font-size: $font-size-4 !important;
}

.font-normal {
  font-weight: 400 !important;
}

.font-medium {
  font-weight: 500 !important;
}

.font-semibold {
  font-weight: 600 !important;
}

.font-bold {
  font-weight: 700 !important;
}

// Width utilities
.w-full {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.w-fit {
  width: fit-content !important;
}

// Height utilities
.h-full {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.h-fit {
  height: fit-content !important;
}

// Position utilities
.relative {
  position: relative !important;
}

.absolute {
  position: absolute !important;
}

.fixed {
  position: fixed !important;
}

.sticky {
  position: sticky !important;
}

// Z-index utilities
.z-0 {
  z-index: 0 !important;
}

.z-10 {
  z-index: 10 !important;
}

.z-20 {
  z-index: 20 !important;
}

.z-30 {
  z-index: 30 !important;
}

.z-40 {
  z-index: 40 !important;
}

.z-50 {
  z-index: 50 !important;
}

// Overflow utilities
.overflow-hidden {
  overflow: hidden !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

// Border radius utilities
.rounded-none {
  border-radius: 0 !important;
}

.rounded-sm {
  border-radius: $border-radius-sm !important;
}

.rounded {
  border-radius: $border-radius-md !important;
}

.rounded-lg {
  border-radius: $border-radius-lg !important;
}

.rounded-full {
  border-radius: $border-radius-full !important;
}
