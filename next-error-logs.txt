$ npm run dev

> next.js-ts-client@0.1.0 dev
> next dev --turbopack

   ▲ Next.js 15.4.6 (Turbopack)
   - Local:        http://localhost:3000
   - Network:      http://**************:3000
   - Experiments (use with caution):
     · optimizePackageImports

 ✓ Starting...
 ✓ Ready in 3.2s
 ○ Compiling / ...
 ✓ Compiled / in 13s
 ⨯ ./app/globals.scss
Error evaluating Node.js code
Error: This variable is available from multiple global modules.
    ╷
4   │ @use 'variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━ includes variable
5   │ @use 'mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━ includes variable
... │
10  │   padding-left: $spacing-4;
    │                 ^^^^^^^^^^ variable use
    ╵
  src\styles\responsive.scss 10:17  @use
  app\globals.scss 3:1              root stylesheet
Caused by: Error: This variable is available from multiple global modules.
    ╷
4   │ @use 'variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━ includes variable
5   │ @use 'mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━ includes variable
... │
10  │   padding-left: $spacing-4;
    │                 ^^^^^^^^^^ variable use
    ╵
  src\styles\responsive.scss 10:17  @use
  app\globals.scss 3:1              root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$1$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86073:19)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86082:19)]
    [at AsyncEnvironment0._async_environment0$_getVariableFromGlobalModule$1 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85640:19)]
    [at AsyncEnvironment0.getVariable$2$namespace (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85633:24)]
    [at _EvaluateVisitor_visitVariableExpression_closure2.call$0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:94072:55)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$1$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91388:23)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91403:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:88852:36]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]



./app/page.module.scss
Error evaluating Node.js code
Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../src/styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../src/styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
6   │   background: linear-gradient(135deg, $primary-blue 0%, $primary-blue-dark 100%);
    │                                       ^^^^^^^^^^^^^ variable use
    ╵
  app\page.module.scss 6:39  root stylesheet
Caused by: Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../src/styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../src/styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
6   │   background: linear-gradient(135deg, $primary-blue 0%, $primary-blue-dark 100%);
    │                                       ^^^^^^^^^^^^^ variable use
    ╵
  app\page.module.scss 6:39  root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$1$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86073:19)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86082:19)]
    [at AsyncEnvironment0._async_environment0$_getVariableFromGlobalModule$1 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85640:19)]
    [at AsyncEnvironment0.getVariable$2$namespace (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85633:24)]
    [at _EvaluateVisitor_visitVariableExpression_closure2.call$0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:94072:55)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$1$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91388:23)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91403:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:88852:36]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]



./src/components/layout/CartButton.module.scss
Error evaluating Node.js code
Error: Undefined mixin.
   ╷
11 │   @include flexbox(center, center);
   │   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   ╵
  src\components\layout\CartButton.module.scss 11:3  root stylesheet
Caused by: Error: Undefined mixin.
   ╷
11 │   @include flexbox(center, center);
   │   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   ╵
  src\components\layout\CartButton.module.scss 11:3  root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87830:25]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]
    [at _wrapJsFunctionForAsync_closure.call$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:38028:12)]
    [at Object._asyncStartSync (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4885:20)]
    [at _EvaluateVisitor2._applyMixin$body$_EvaluateVisitor0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87888:16)]
    [at _EvaluateVisitor2._async_evaluate0$_applyMixin$5 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87816:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87908:48]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]
    [at _wrapJsFunctionForAsync_closure.call$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:38028:12)]



./src/components/layout/Footer.module.scss
Error evaluating Node.js code
Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../../styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../../styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
5   │   background-color: $primary-dark;
    │                     ^^^^^^^^^^^^^ variable use
    ╵
  src\components\layout\Footer.module.scss 5:21  root stylesheet
Caused by: Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../../styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../../styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
5   │   background-color: $primary-dark;
    │                     ^^^^^^^^^^^^^ variable use
    ╵
  src\components\layout\Footer.module.scss 5:21  root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$1$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86073:19)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86082:19)]
    [at AsyncEnvironment0._async_environment0$_getVariableFromGlobalModule$1 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85640:19)]
    [at AsyncEnvironment0.getVariable$2$namespace (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85633:24)]
    [at _EvaluateVisitor_visitVariableExpression_closure2.call$0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:94072:55)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$1$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91388:23)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91403:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:88852:36]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]



./src/components/layout/Header.module.scss
Error evaluating Node.js code
Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../../styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../../styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
6   │   border-bottom: 1px solid $gray-200;
    │                            ^^^^^^^^^ variable use
    ╵
  src\components\layout\Header.module.scss 6:28  root stylesheet
Caused by: Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../../styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../../styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
6   │   border-bottom: 1px solid $gray-200;
    │                            ^^^^^^^^^ variable use
    ╵
  src\components\layout\Header.module.scss 6:28  root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$1$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86073:19)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86082:19)]
    [at AsyncEnvironment0._async_environment0$_getVariableFromGlobalModule$1 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85640:19)]
    [at AsyncEnvironment0.getVariable$2$namespace (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85633:24)]
    [at _EvaluateVisitor_visitVariableExpression_closure2.call$0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:94072:55)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$1$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91388:23)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91403:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:88852:36]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]



./src/components/layout/SearchBar.module.scss
Error evaluating Node.js code
Error: Undefined mixin.
  ╷
2 │   @include flexbox(flex-start, center);
  │   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  ╵
  src\components\layout\SearchBar.module.scss 2:3  root stylesheet
Caused by: Error: Undefined mixin.
  ╷
2 │   @include flexbox(flex-start, center);
  │   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  ╵
  src\components\layout\SearchBar.module.scss 2:3  root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87830:25]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]
    [at _wrapJsFunctionForAsync_closure.call$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:38028:12)]
    [at Object._asyncStartSync (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4885:20)]
    [at _EvaluateVisitor2._applyMixin$body$_EvaluateVisitor0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87888:16)]
    [at _EvaluateVisitor2._async_evaluate0$_applyMixin$5 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87816:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87908:48]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]
    [at _wrapJsFunctionForAsync_closure.call$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:38028:12)]



./src/components/layout/UserMenu.module.scss
Error evaluating Node.js code
Error: Undefined mixin.
  ╷
6 │   @include flexbox(flex-start, center);
  │   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  ╵
  src\components\layout\UserMenu.module.scss 6:3  root stylesheet
Caused by: Error: Undefined mixin.
  ╷
6 │   @include flexbox(flex-start, center);
  │   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  ╵
  src\components\layout\UserMenu.module.scss 6:3  root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87830:25]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]
    [at _wrapJsFunctionForAsync_closure.call$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:38028:12)]
    [at Object._asyncStartSync (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4885:20)]
    [at _EvaluateVisitor2._applyMixin$body$_EvaluateVisitor0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87888:16)]
    [at _EvaluateVisitor2._async_evaluate0$_applyMixin$5 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87816:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87908:48]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]
    [at _wrapJsFunctionForAsync_closure.call$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:38028:12)]



./src/components/providers/NotificationProvider.module.scss
Error evaluating Node.js code
Error: Undefined variable.
  ╷
3 │   top: $spacing-4;
  │        ^^^^^^^^^^
  ╵
  src\components\providers\NotificationProvider.module.scss 3:8  root stylesheet
Caused by: Error: Undefined variable.
  ╷
3 │   top: $spacing-4;
  │        ^^^^^^^^^^
  ╵
  src\components\providers\NotificationProvider.module.scss 3:8  root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:88859:23]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]
    [at _wrapJsFunctionForAsync_closure.call$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:38028:12)]
    [at Object._asyncStartSync (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4885:20)]
    [at _EvaluateVisitor2.visitVariableExpression$body$_EvaluateVisitor0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:88865:16)]
    [at _EvaluateVisitor2.visitVariableExpression$1 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:88839:19)]
    [at VariableExpression0.accept$1$1 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:124423:22)]
    [at VariableExpression0.accept$1 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:124426:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86923:41]



./src/components/ui/Button.module.scss
Error evaluating Node.js code
Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../../styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../../styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
10  │   border-radius: $border-radius-md;
    │                  ^^^^^^^^^^^^^^^^^ variable use
    ╵
  src\components\ui\Button.module.scss 10:18  root stylesheet
Caused by: Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../../styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../../styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
10  │   border-radius: $border-radius-md;
    │                  ^^^^^^^^^^^^^^^^^ variable use
    ╵
  src\components\ui\Button.module.scss 10:18  root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$1$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86073:19)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86082:19)]
    [at AsyncEnvironment0._async_environment0$_getVariableFromGlobalModule$1 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85640:19)]
    [at AsyncEnvironment0.getVariable$2$namespace (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85633:24)]
    [at _EvaluateVisitor_visitVariableExpression_closure2.call$0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:94072:55)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$1$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91388:23)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91403:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:88852:36]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]



./src/components/ui/Input.module.scss
Error evaluating Node.js code
Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../../styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../../styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
7   │   gap: $spacing-1;
    │        ^^^^^^^^^^ variable use
    ╵
  src\components\ui\Input.module.scss 7:8  root stylesheet
Caused by: Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../../styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../../styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
7   │   gap: $spacing-1;
    │        ^^^^^^^^^^ variable use
    ╵
  src\components\ui\Input.module.scss 7:8  root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$1$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86073:19)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86082:19)]
    [at AsyncEnvironment0._async_environment0$_getVariableFromGlobalModule$1 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85640:19)]
    [at AsyncEnvironment0.getVariable$2$namespace (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85633:24)]
    [at _EvaluateVisitor_visitVariableExpression_closure2.call$0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:94072:55)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$1$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91388:23)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91403:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:88852:36]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]



./src/components/ui/Modal.module.scss
Error evaluating Node.js code
Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../../styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../../styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
11  │   z-index: $z-modal-backdrop;
    │            ^^^^^^^^^^^^^^^^^ variable use
    ╵
  src\components\ui\Modal.module.scss 11:12  root stylesheet
Caused by: Error: This variable is available from multiple global modules.
    ╷
1   │ @use '../../styles/variables' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
2   │ @use '../../styles/mixins' as *;
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ includes variable
... │
11  │   z-index: $z-modal-backdrop;
    │            ^^^^^^^^^^^^^^^^^ variable use
    ╵
  src\components\ui\Modal.module.scss 11:12  root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$1$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86073:19)]
    [at AsyncEnvironment0._async_environment0$_fromOneModule$3 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:86082:19)]
    [at AsyncEnvironment0._async_environment0$_getVariableFromGlobalModule$1 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85640:19)]
    [at AsyncEnvironment0.getVariable$2$namespace (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:85633:24)]
    [at _EvaluateVisitor_visitVariableExpression_closure2.call$0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:94072:55)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$1$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91388:23)]
    [at _EvaluateVisitor2._async_evaluate0$_addExceptionSpan$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:91403:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:88852:36]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]



./src/components/ui/NotificationToast.module.scss
Error evaluating Node.js code
Error: Undefined mixin.
  ╷
2 │   @include flexbox(flex-start, flex-start);
  │   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  ╵
  src\components\ui\NotificationToast.module.scss 2:3  root stylesheet
Caused by: Error: Undefined mixin.
  ╷
2 │   @include flexbox(flex-start, flex-start);
  │   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  ╵
  src\components\ui\NotificationToast.module.scss 2:3  root stylesheet
    [at Object.wrapException (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:2302:47)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87830:25]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]
    [at _wrapJsFunctionForAsync_closure.call$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:38028:12)]
    [at Object._asyncStartSync (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4885:20)]
    [at _EvaluateVisitor2._applyMixin$body$_EvaluateVisitor0 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87888:16)]
    [at _EvaluateVisitor2._async_evaluate0$_applyMixin$5 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87816:19)]
    [at C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:87908:48]
    [at _wrapJsFunctionForAsync_closure.$protected (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:4921:15)]
    [at _wrapJsFunctionForAsync_closure.call$2 (C:\Users\<USER>\Downloads\Projects\Projects Repos\picky-ecommerce-app\picky-store\next.js-ts-client\node_modules\sass\sass.dart.js:38028:12)]



./src/components/providers/Providers.tsx:9:1
Module not found: Can't resolve '../../lib/query-client'
   7 | import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
   8 | import { useState } from 'react';
>  9 | import { queryClient } from '../../lib/query-client';
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  10 | import { NotificationProvider } from './NotificationProvider';
  11 | import { ModalProvider } from './ModalProvider';
  12 | import { AuthProvider } from './AuthProvider';



Import trace:
  ./src/components/providers/Providers.tsx
  ./app/layout.tsx

https://nextjs.org/docs/messages/module-not-found


 ⚠ ./app/globals.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9  @use
app\globals.scss 2:1        root stylesheet



 ⚠ ./app/page.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9  @use
app\page.module.scss 2:1    root stylesheet



 ⚠ ./src/components/layout/Footer.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9                    @use
src\components\layout\Footer.module.scss 2:1  root stylesheet



 ⚠ ./src/components/layout/Header.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9                    @use
src\components\layout\Header.module.scss 2:1  root stylesheet



 ⚠ ./src/components/ui/Button.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9                @use
src\components\ui\Button.module.scss 2:1  root stylesheet



 ⚠ ./src/components/ui/Input.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9               @use
src\components\ui\Input.module.scss 2:1  root stylesheet



 ⚠ ./src/components/ui/Modal.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9               @use
src\components\ui\Modal.module.scss 2:1  root stylesheet



 ○ Compiling /_error ...
 ✓ Compiled /_error in 1960ms
 GET / 500 in 15539ms
 ⚠ ./app/globals.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9  @use
app\globals.scss 2:1        root stylesheet



 ⚠ ./app/page.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9  @use
app\page.module.scss 2:1    root stylesheet



 ⚠ ./src/components/layout/Footer.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9                    @use
src\components\layout\Footer.module.scss 2:1  root stylesheet



 ⚠ ./src/components/layout/Header.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9                    @use
src\components\layout\Header.module.scss 2:1  root stylesheet



 ⚠ ./src/components/ui/Button.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9                @use
src\components\ui\Button.module.scss 2:1  root stylesheet



 ⚠ ./src/components/ui/Input.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9               @use
src\components\ui\Input.module.scss 2:1  root stylesheet



 ⚠ ./src/components/ui/Modal.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9               @use
src\components\ui\Modal.module.scss 2:1  root stylesheet



 ○ Compiling /favicon.ico ...
 ✓ Compiled /favicon.ico in 730ms
 ⚠ ./app/globals.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9  @use
app\globals.scss 2:1        root stylesheet



 ⚠ ./app/page.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9  @use
app\page.module.scss 2:1    root stylesheet



 ⚠ ./src/components/layout/Footer.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9                    @use
src\components\layout\Footer.module.scss 2:1  root stylesheet



 ⚠ ./src/components/layout/Header.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9                    @use
src\components\layout\Header.module.scss 2:1  root stylesheet



 ⚠ ./src/components/ui/Button.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9                @use
src\components\ui\Button.module.scss 2:1  root stylesheet



 ⚠ ./src/components/ui/Input.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9               @use
src\components\ui\Input.module.scss 2:1  root stylesheet



 ⚠ ./src/components/ui/Modal.module.scss
Issue while running loader
SassWarning: Deprecation Warning on line 0, column 8 of file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/styles/mixins.scss:0:8:
Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

0 | @import './variables';


src\styles\mixins.scss 1:9               @use
src\components\ui\Modal.module.scss 2:1  root stylesheet



 GET /favicon.ico 500 in 833ms