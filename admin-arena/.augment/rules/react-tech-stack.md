---
type: "agent_requested"
description: "Tech stack for admin-arena React app"
---
- Use React Hook Forms when dealing with forms.
- Use schema validators, Zod for data validation.
- Use Tanstack Router for routing.
- Use TanStack Query as the client-side data-fetching library.
- Use <PERSON>ustand for client-side state management.
- Use React Select for select input controls.  
- Use luxon as a date/time formatter.
