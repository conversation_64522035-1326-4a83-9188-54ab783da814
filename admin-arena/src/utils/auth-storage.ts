// Pure cookie-based authentication utility
// HTTP-only cookies are managed entirely by the server
// Frontend has no access to tokens for security

export class AuthStorage {
  /**
   * Check if user appears to be authenticated
   * This is a basic check - actual authentication is handled server-side
   * We can't access HTTP-only cookies from JavaScript
   */
  static isAuthenticated(): boolean {
    // Since we can't access HTTP-only cookies, we'll rely on API calls
    // to determine authentication status. This method is kept for compatibility
    // but will be replaced by server-side checks
    return true // Will be determined by API responses
  }

  /**
   * Clear any legacy cookies that might exist
   * HTTP-only cookies are cleared by the server during logout
   */
  static clearLegacyCookies(): void {
    // Clear any old cookies that might have been set by previous versions
    // Note: HTTP-only cookies cannot be cleared by JavaScript, but we clear any legacy non-HTTP-only cookies
    document.cookie = 'admin_access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; samesite=None;'
    document.cookie = 'admin_refresh_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; samesite=None;'
    document.cookie = 'access=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
    document.cookie = 'refresh=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
  }

  // Legacy methods kept for compatibility - they now do nothing
  // since tokens are in HTTP-only cookies managed by server

  static setTokens(): void {
    // No-op: Server sets HTTP-only cookies
  }

  static getAccessToken(): string | null {
    // Cannot access HTTP-only cookies from JavaScript
    return null
  }

  static getRefreshToken(): string | null {
    // Cannot access HTTP-only cookies from JavaScript
    return null
  }

  static clearTokens(): void {
    // Server clears HTTP-only cookies during logout
    this.clearLegacyCookies()
  }

  static hasValidTokens(): boolean {
    // Cannot determine from frontend - server handles this
    return false
  }

  static updateAccessToken(): void {
    // No-op: Server manages token refresh
  }

  static isAccessTokenExpired(): boolean {
    // Cannot determine from frontend
    return false
  }

  static getTokenExpiration(): Date | null {
    // Cannot access token from frontend
    return null
  }

  static getUserFromToken(): null {
    // Cannot access token from frontend
    return null
  }
}
