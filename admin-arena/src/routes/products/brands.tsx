// Brands management route
import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../../components/auth/AuthGuard'
import { BrandsPage } from '../../pages/products/BrandsPage'

export const Route = createFileRoute('/products/brands')({
  component: BrandsRoute,
})

function BrandsRoute() {
  return (
    <AuthGuard permission="staff.view_brand">
      <BrandsPage />
    </AuthGuard>
  )
}
