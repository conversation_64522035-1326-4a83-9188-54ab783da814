// Analytics route with authentication and permission guards
// Handles analytics and reporting functionality

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../components/auth/AuthGuard'
import { AnalyticsPage } from '../pages/analytics/AnalyticsPage'

export const Route = createFileRoute('/analytics')({
  component: AnalyticsRoute,
})

function AnalyticsRoute() {
  return (
    <AuthGuard permission="staff.view_analytics">
      <AnalyticsPage />
    </AuthGuard>
  )
}
