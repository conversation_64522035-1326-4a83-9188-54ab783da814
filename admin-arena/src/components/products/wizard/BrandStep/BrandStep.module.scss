@use '../../../../scss/variables' as *;
@use '../../../../scss/mixins' as *;

.container {
  @include flex-column;
  gap: $spacing-6;
}

.header {
  @include flex-between;
  gap: $spacing-4;
  
  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.headerContent {
  flex: 1;
  
  h3 {
    margin: 0 0 $spacing-2 0;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
  }
  
  p {
    margin: 0;
    color: $gray-600;
    font-size: $font-size-sm;
    line-height: 1.5;
  }
}

.selectedCard {
  background-color: $green-50;
  border: 1px solid $green-200;
}

.selectedBrand {
  @include flex-between;
  gap: $spacing-4;
}

.selectedLogo {
  @include flex-center;
  width: 48px;
  height: 48px;
  background-color: $green-100;
  border-radius: $border-radius;
  color: $green-600;
  flex-shrink: 0;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  svg {
    width: 24px;
    height: 24px;
  }
}

.selectedInfo {
  flex: 1;
  
  h4 {
    margin: 0 0 $spacing-1 0;
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    color: $green-800;
  }
  
  p {
    margin: 0;
    font-size: $font-size-base;
    color: $green-700;
  }
}

.brandsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: $spacing-4;
  
  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.brandCard {
  position: relative;
  transition: all 0.2s ease-in-out;
  
  &.selected {
    border-color: $primary-300;
    background-color: $primary-50;
  }
  
  &:hover:not(.selected) {
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }
}

.brandContent {
  @include flex-column;
  gap: $spacing-4;
  text-align: center;
}

.brandLogo {
  @include flex-center;
  width: 80px;
  height: 80px;
  background-color: $gray-100;
  border-radius: $border-radius-lg;
  color: $gray-400;
  margin: 0 auto;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  svg {
    width: 32px;
    height: 32px;
  }
}

.brandInfo {
  flex: 1;
}

.brandName {
  margin: 0 0 $spacing-1 0;
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $gray-900;
}

.brandStats {
  margin: 0;
  font-size: $font-size-sm;
  color: $gray-600;
}

.selectedBadge {
  position: absolute;
  top: $spacing-3;
  right: $spacing-3;
  @include flex-center;
  width: 24px;
  height: 24px;
  background-color: $primary-500;
  color: white;
  border-radius: $border-radius-full;
  
  svg {
    width: 14px;
    height: 14px;
  }
}

.selectButton {
  width: 100%;
  justify-content: center;
}

.emptyState {
  grid-column: 1 / -1;
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-8;
  text-align: center;
  color: $gray-500;
  
  svg {
    width: 48px;
    height: 48px;
    color: $gray-400;
  }
  
  h4 {
    margin: 0;
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $gray-700;
  }
  
  p {
    margin: 0;
    font-size: $font-size-sm;
    color: $gray-500;
  }
}

.loading {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-8;
  
  p {
    margin: 0;
    color: $gray-600;
    font-size: $font-size-base;
  }
}

// Create modal styles
.createModal {
  @include flex-column;
  gap: $spacing-4;
}

.formGroup {
  @include flex-column;
  gap: $spacing-2;
  
  label {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $gray-700;
  }
}

.logoUpload {
  position: relative;
}

.fileInput {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.uploadArea {
  @include flex-center;
  width: 100%;
  height: 120px;
  border: 2px dashed $gray-300;
  border-radius: $border-radius;
  background-color: $gray-50;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    border-color: $primary-400;
    background-color: $primary-50;
  }
}

.logoPreview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: $border-radius;
}

.uploadPlaceholder {
  @include flex-column-center;
  gap: $spacing-2;
  color: $gray-500;
  
  svg {
    width: 32px;
    height: 32px;
  }
  
  span {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
  }
}

.modalActions {
  @include flex-end;
  gap: $spacing-3;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
  
  @include mobile-only {
    flex-direction: column;
    
    button {
      width: 100%;
      justify-content: center;
    }
  }
}

// Responsive adjustments
@include mobile-only {
  .brandsGrid {
    grid-template-columns: 1fr;
  }
  
  .brandCard {
    &:hover:not(.selected) {
      transform: none;
    }
  }
}
