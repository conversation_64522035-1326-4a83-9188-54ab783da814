// Variants creation step in product wizard
// Allows creating product variants based on selected attributes

import React, { useState, useEffect } from 'react'
import { FiPlus, FiTrash2, FiPackage, FiDollarSign } from 'react-icons/fi'
import { Card } from '../../../ui/Card'
import { Button } from '../../../ui/Button'
import { Input } from '../../../ui/Input'
import { Switch } from '../../../ui/Switch'
import { Badge } from '../../../ui/Badge'
import { WizardData } from '../../../../pages/products/ProductWizardPage'
import styles from './VariantsStep.module.scss'

interface VariantsStepProps {
  data: WizardData
  onUpdate: (data: Partial<WizardData>) => void
}

interface VariantFormData {
  sku: string
  price: number
  compare_at_price?: number
  quantity_available: number
  is_active: boolean
  attribute_values: Array<{
    attribute_id: number
    value_id: number
  }>
}

export const VariantsStep: React.FC<VariantsStepProps> = ({ data, onUpdate }) => {
  const [variants, setVariants] = useState<VariantFormData[]>(
    data.variants || []
  )

  // Generate default variant if none exist
  useEffect(() => {
    if (variants.length === 0) {
      const defaultVariant: VariantFormData = {
        sku: `${data.product?.slug || 'product'}-001`,
        price: 0,
        compare_at_price: undefined,
        quantity_available: 0,
        is_active: true,
        attribute_values: []
      }
      setVariants([defaultVariant])
    }
  }, [variants.length, data.product?.slug])

  // Update wizard data when variants change
  useEffect(() => {
    onUpdate({ variants })
  }, [variants, onUpdate])

  const addVariant = () => {
    const newVariant: VariantFormData = {
      sku: `${data.product?.slug || 'product'}-${String(variants.length + 1).padStart(3, '0')}`,
      price: 0,
      compare_at_price: undefined,
      quantity_available: 0,
      is_active: true,
      attribute_values: []
    }
    setVariants([...variants, newVariant])
  }

  const removeVariant = (index: number) => {
    if (variants.length > 1) {
      setVariants(variants.filter((_, i) => i !== index))
    }
  }

  const updateVariant = (index: number, field: keyof VariantFormData, value: any) => {
    const updatedVariants = variants.map((variant, i) => 
      i === index ? { ...variant, [field]: value } : variant
    )
    setVariants(updatedVariants)
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h3>Product Variants</h3>
          <p>Create different variants of your product with unique SKUs, prices, and inventory.</p>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={addVariant}
        >
          <FiPlus />
          Add Variant
        </Button>
      </div>

      <div className={styles.variantsContainer}>
        {variants.map((variant, index) => (
          <Card key={index} className={styles.variantCard}>
            <div className={styles.variantHeader}>
              <div className={styles.variantTitle}>
                <FiPackage />
                <h4>Variant {index + 1}</h4>
              </div>
              
              {variants.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeVariant(index)}
                  className={styles.removeButton}
                >
                  <FiTrash2 />
                </Button>
              )}
            </div>

            <div className={styles.variantForm}>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label className={styles.label}>SKU *</label>
                  <Input
                    value={variant.sku}
                    onChange={(e) => updateVariant(index, 'sku', e.target.value)}
                    placeholder="Enter SKU"
                  />
                </div>

                <div className={styles.formGroup}>
                  <label className={styles.label}>Quantity Available *</label>
                  <Input
                    type="number"
                    value={variant.quantity_available}
                    onChange={(e) => updateVariant(index, 'quantity_available', parseInt(e.target.value) || 0)}
                    placeholder="0"
                    min="0"
                  />
                </div>
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label className={styles.label}>Price *</label>
                  <div className={styles.priceInput}>
                    <FiDollarSign className={styles.priceIcon} />
                    <Input
                      type="number"
                      value={variant.price}
                      onChange={(e) => updateVariant(index, 'price', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label className={styles.label}>Compare at Price</label>
                  <div className={styles.priceInput}>
                    <FiDollarSign className={styles.priceIcon} />
                    <Input
                      type="number"
                      value={variant.compare_at_price || ''}
                      onChange={(e) => updateVariant(index, 'compare_at_price', parseFloat(e.target.value) || undefined)}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>
              </div>

              <div className={styles.switchGroup}>
                <div className={styles.switchInfo}>
                  <label className={styles.label}>Active Variant</label>
                  <span className={styles.helpText}>
                    Active variants are available for purchase
                  </span>
                </div>
                <Switch
                  checked={variant.is_active}
                  onChange={(e) => updateVariant(index, 'is_active', e.target.checked)}
                />
              </div>

              {data.attributes && data.attributes.length > 0 && (
                <div className={styles.attributesSection}>
                  <h5>Attribute Values</h5>
                  <p className={styles.helpText}>
                    Select specific values for each attribute for this variant
                  </p>
                  <div className={styles.attributesList}>
                    {data.attributes.map((attribute) => (
                      <div key={attribute.id} className={styles.attributeItem}>
                        <span className={styles.attributeName}>{attribute.name}:</span>
                        <Badge variant="secondary">Not configured</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </Card>
        ))}
      </div>

      <div className={styles.summary}>
        <Card className={styles.summaryCard}>
          <h4>Variants Summary</h4>
          <div className={styles.summaryStats}>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Total Variants:</span>
              <span className={styles.statValue}>{variants.length}</span>
            </div>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Active Variants:</span>
              <span className={styles.statValue}>
                {variants.filter(v => v.is_active).length}
              </span>
            </div>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Total Inventory:</span>
              <span className={styles.statValue}>
                {variants.reduce((sum, v) => sum + v.quantity_available, 0)}
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}
