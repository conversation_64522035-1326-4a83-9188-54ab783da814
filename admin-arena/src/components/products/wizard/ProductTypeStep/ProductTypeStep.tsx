// Product type selection step in product wizard
// Allows selecting or creating product types with attribute associations

import React, { useState } from 'react'
import { FiPlus, FiSettings, FiCheck } from 'react-icons/fi'
import { useProductTypes, useCreateProductType } from '../../../../hooks/use-products'
import { Card } from '../../../ui/Card'
import { Button } from '../../../ui/Button'
import { Input } from '../../../ui/Input'
import { Modal } from '../../../ui/Modal'
import { Badge } from '../../../ui/Badge'
import { LoadingSpinner } from '../../../ui/LoadingSpinner'
import { WizardData } from '../../../../pages/products/ProductWizardPage'
import { ProductType } from '../../../../types/api-types'
import styles from './ProductTypeStep.module.scss'

interface ProductTypeStepProps {
  data: WizardData
  onUpdate: (data: Partial<WizardData>) => void
}

export const ProductTypeStep: React.FC<ProductTypeStepProps> = ({ data, onUpdate }) => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [newTypeName, setNewTypeName] = useState('')
  const [newTypeSlug, setNewTypeSlug] = useState('')

  const { data: productTypes, isLoading, refetch } = useProductTypes()
  const createProductTypeMutation = useCreateProductType()

  const handleProductTypeSelect = (productType: ProductType) => {
    onUpdate({
      productType: {
        id: productType.id,
        name: productType.title,
        slug: productType.title.toLowerCase().replace(/\s+/g, '-'),
      }
    })
  }

  const handleNameChange = (name: string) => {
    setNewTypeName(name)
    // Auto-generate slug
    const slug = name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
    setNewTypeSlug(slug)
  }

  const handleCreateProductType = async () => {
    if (!newTypeName.trim() || !newTypeSlug.trim()) return

    try {
      await createProductTypeMutation.mutateAsync({
        title: newTypeName,
      })

      setIsCreateModalOpen(false)
      setNewTypeName('')
      setNewTypeSlug('')
      refetch()
    } catch (error) {
      console.error('Failed to create product type:', error)
    }
  }

  if (isLoading) {
    return (
      <div className={styles.loading}>
        <LoadingSpinner size="lg" />
        <p>Loading product types...</p>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h3>Select Product Type</h3>
          <p>Product types define the structure and attributes that products can have. Choose the type that best matches your product's characteristics.</p>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsCreateModalOpen(true)}
        >
          <FiPlus />
          New Type
        </Button>
      </div>

      {data.productType && (
        <Card className={styles.selectedCard}>
          <div className={styles.selectedType}>
            <div className={styles.selectedIcon}>
              <FiSettings />
            </div>
            <div className={styles.selectedInfo}>
              <h4>Selected Product Type</h4>
              <p>{data.productType.name}</p>
              <span className={styles.slug}>{data.productType.slug}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onUpdate({ productType: undefined })}
            >
              Change
            </Button>
          </div>
        </Card>
      )}

      <div className={styles.typesGrid}>
        {productTypes && productTypes.length > 0 ? (
          productTypes.map((productType) => {
            const isSelected = data.productType?.id === productType.id

            return (
              <Card
                key={productType.id}
                className={`${styles.typeCard} ${isSelected ? styles.selected : ''}`}
                hover={!isSelected}
              >
                <div className={styles.typeContent}>
                  <div className={styles.typeHeader}>
                    <div className={styles.typeIcon}>
                      <FiSettings />
                    </div>
                    <div className={styles.typeInfo}>
                      <h4 className={styles.typeName}>{productType.title}</h4>
                      <p className={styles.typeSlug}>{productType.title}</p>
                    </div>
                    {isSelected && (
                      <div className={styles.selectedBadge}>
                        <FiCheck />
                      </div>
                    )}
                  </div>

                  {productType.attributes_count && productType.attributes_count > 0 && (
                    <div className={styles.attributes}>
                      <h5>Attributes:</h5>
                      <div className={styles.attributesList}>
                        <Badge variant="secondary" size="sm">
                          {productType.attributes_count} attributes
                        </Badge>
                      </div>
                    </div>
                  )}

                  <div className={styles.typeStats}>
                    <span className={styles.stat}>
                      {productType.products_count || 0} products
                    </span>
                    <span className={styles.stat}>
                      {productType.attributes_count || 0} attributes
                    </span>
                  </div>

                  <Button
                    variant={isSelected ? "primary" : "outline"}
                    size="sm"
                    onClick={() => handleProductTypeSelect(productType)}
                    className={styles.selectButton}
                  >
                    {isSelected ? 'Selected' : 'Select Type'}
                  </Button>
                </div>
              </Card>
            )
          })
        ) : (
          <div className={styles.emptyState}>
            <FiSettings />
            <h4>No product types found</h4>
            <p>Create your first product type to define product structure</p>
            <Button
              variant="primary"
              onClick={() => setIsCreateModalOpen(true)}
            >
              <FiPlus />
              Create Product Type
            </Button>
          </div>
        )}
      </div>

      {/* Create Product Type Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Product Type"
        size="md"
      >
        <div className={styles.createModal}>
          <div className={styles.formGroup}>
            <label htmlFor="typeName">Product Type Name</label>
            <Input
              id="typeName"
              type="text"
              value={newTypeName}
              onChange={(e) => handleNameChange(e.target.value)}
              placeholder="e.g., T-Shirt, Book, Electronics"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="typeSlug">Slug</label>
            <Input
              id="typeSlug"
              type="text"
              value={newTypeSlug}
              onChange={(e) => setNewTypeSlug(e.target.value)}
              placeholder="product-type-slug"
              help="URL-friendly version of the name"
            />
          </div>

          <div className={styles.modalActions}>
            <Button
              variant="outline"
              onClick={() => setIsCreateModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleCreateProductType}
              disabled={!newTypeName.trim() || !newTypeSlug.trim() || createProductTypeMutation.isPending}
            >
              {createProductTypeMutation.isPending ? 'Creating...' : 'Create Type'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}
