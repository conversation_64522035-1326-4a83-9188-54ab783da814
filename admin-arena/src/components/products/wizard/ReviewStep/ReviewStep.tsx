// Review step in product wizard
// Shows a comprehensive summary of all entered data before final submission

import React from 'react'
import { FiCheck, FiEdit, FiPackage, FiTag, FiGrid, FiTool, FiDollarSign } from 'react-icons/fi'
import { Card } from '../../../ui/Card'
import { Button } from '../../../ui/Button'
import { Badge } from '../../../ui/Badge'
import { WizardData } from '../../../../pages/products/ProductWizardPage'
import styles from './ReviewStep.module.scss'

interface ReviewStepProps {
  data: WizardData
  onEdit: (step: number) => void
  onSubmit: () => void
  isSubmitting: boolean
}

export const ReviewStep: React.FC<ReviewStepProps> = ({ 
  data, 
  onEdit, 
  onSubmit, 
  isSubmitting 
}) => {
  const isComplete = (condition: boolean) => condition

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h3>Review & Create Product</h3>
          <p>Review all the information below and click "Create Product" to finalize.</p>
        </div>
      </div>

      <div className={styles.reviewSections}>
        {/* Category Section */}
        <Card className={styles.reviewCard}>
          <div className={styles.cardHeader}>
            <div className={styles.cardTitle}>
              <FiGrid />
              <h4>Category</h4>
              {isComplete(!!data.category) && (
                <Badge variant="success" size="sm">
                  <FiCheck />
                  Complete
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(0)}
            >
              <FiEdit />
              Edit
            </Button>
          </div>
          
          <div className={styles.cardContent}>
            {data.category ? (
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Selected Category:</span>
                <span className={styles.infoValue}>{data.category.name}</span>
              </div>
            ) : (
              <div className={styles.incomplete}>No category selected</div>
            )}
          </div>
        </Card>

        {/* Product Type Section */}
        <Card className={styles.reviewCard}>
          <div className={styles.cardHeader}>
            <div className={styles.cardTitle}>
              <FiTag />
              <h4>Product Type</h4>
              {isComplete(!!data.productType) && (
                <Badge variant="success" size="sm">
                  <FiCheck />
                  Complete
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(1)}
            >
              <FiEdit />
              Edit
            </Button>
          </div>
          
          <div className={styles.cardContent}>
            {data.productType ? (
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Selected Type:</span>
                <span className={styles.infoValue}>{data.productType.name}</span>
              </div>
            ) : (
              <div className={styles.incomplete}>No product type selected</div>
            )}
          </div>
        </Card>

        {/* Brand Section */}
        <Card className={styles.reviewCard}>
          <div className={styles.cardHeader}>
            <div className={styles.cardTitle}>
              <FiTag />
              <h4>Brand</h4>
              {isComplete(!!data.brand) && (
                <Badge variant="success" size="sm">
                  <FiCheck />
                  Complete
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(2)}
            >
              <FiEdit />
              Edit
            </Button>
          </div>
          
          <div className={styles.cardContent}>
            {data.brand ? (
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Selected Brand:</span>
                <span className={styles.infoValue}>{data.brand.name}</span>
              </div>
            ) : (
              <div className={styles.incomplete}>No brand selected</div>
            )}
          </div>
        </Card>

        {/* Attributes Section */}
        <Card className={styles.reviewCard}>
          <div className={styles.cardHeader}>
            <div className={styles.cardTitle}>
              <FiTool />
              <h4>Attributes</h4>
              {isComplete(!!data.attributes && data.attributes.length > 0) && (
                <Badge variant="success" size="sm">
                  <FiCheck />
                  Complete
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(3)}
            >
              <FiEdit />
              Edit
            </Button>
          </div>
          
          <div className={styles.cardContent}>
            {data.attributes && data.attributes.length > 0 ? (
              <div className={styles.attributesList}>
                {data.attributes.map((attribute) => (
                  <div key={attribute.id} className={styles.attributeItem}>
                    <span>{attribute.name}</span>
                    <Badge variant="secondary" size="sm">
                      {attribute.values.length} values
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className={styles.incomplete}>No attributes selected</div>
            )}
          </div>
        </Card>

        {/* Product Information Section */}
        <Card className={styles.reviewCard}>
          <div className={styles.cardHeader}>
            <div className={styles.cardTitle}>
              <FiPackage />
              <h4>Product Information</h4>
              {isComplete(!!data.product?.name && !!data.product?.description) && (
                <Badge variant="success" size="sm">
                  <FiCheck />
                  Complete
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(4)}
            >
              <FiEdit />
              Edit
            </Button>
          </div>
          
          <div className={styles.cardContent}>
            {data.product ? (
              <div className={styles.infoList}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Name:</span>
                  <span className={styles.infoValue}>{data.product.name || 'Not set'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Slug:</span>
                  <span className={styles.infoValue}>{data.product.slug || 'Not set'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Status:</span>
                  <Badge variant={data.product.is_active ? 'success' : 'secondary'} size="sm">
                    {data.product.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Description:</span>
                  <span className={styles.infoValue}>
                    {data.product.description ? 
                      `${data.product.description.substring(0, 100)}${data.product.description.length > 100 ? '...' : ''}` 
                      : 'Not set'
                    }
                  </span>
                </div>
              </div>
            ) : (
              <div className={styles.incomplete}>No product information entered</div>
            )}
          </div>
        </Card>

        {/* Variants Section */}
        <Card className={styles.reviewCard}>
          <div className={styles.cardHeader}>
            <div className={styles.cardTitle}>
              <FiDollarSign />
              <h4>Product Variants</h4>
              {isComplete(!!data.variants && data.variants.length > 0) && (
                <Badge variant="success" size="sm">
                  <FiCheck />
                  Complete
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(5)}
            >
              <FiEdit />
              Edit
            </Button>
          </div>
          
          <div className={styles.cardContent}>
            {data.variants && data.variants.length > 0 ? (
              <div className={styles.variantsList}>
                <div className={styles.variantsSummary}>
                  <div className={styles.summaryItem}>
                    <span>Total Variants:</span>
                    <span>{data.variants.length}</span>
                  </div>
                  <div className={styles.summaryItem}>
                    <span>Active Variants:</span>
                    <span>{data.variants.filter(v => v.is_active).length}</span>
                  </div>
                  <div className={styles.summaryItem}>
                    <span>Total Inventory:</span>
                    <span>{data.variants.reduce((sum, v) => sum + v.quantity_available, 0)}</span>
                  </div>
                </div>
                
                <div className={styles.variantsPreview}>
                  {data.variants.slice(0, 3).map((variant, index) => (
                    <div key={index} className={styles.variantPreview}>
                      <span className={styles.variantSku}>{variant.sku}</span>
                      <span className={styles.variantPrice}>${variant.price.toFixed(2)}</span>
                      <Badge variant={variant.is_active ? 'success' : 'secondary'} size="sm">
                        {variant.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  ))}
                  {data.variants.length > 3 && (
                    <div className={styles.moreVariants}>
                      +{data.variants.length - 3} more variants
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className={styles.incomplete}>No variants created</div>
            )}
          </div>
        </Card>
      </div>

      <div className={styles.submitSection}>
        <Card className={styles.submitCard}>
          <div className={styles.submitContent}>
            <div className={styles.submitInfo}>
              <h4>Ready to Create Product?</h4>
              <p>
                Once you create the product, it will be added to your inventory and 
                {data.product?.is_active ? ' will be visible to customers.' : ' will be saved as inactive.'}
              </p>
            </div>
            
            <Button
              variant="primary"
              size="lg"
              onClick={onSubmit}
              loading={isSubmitting}
              className={styles.submitButton}
            >
              <FiPackage />
              Create Product
            </Button>
          </div>
        </Card>
      </div>
    </div>
  )
}
