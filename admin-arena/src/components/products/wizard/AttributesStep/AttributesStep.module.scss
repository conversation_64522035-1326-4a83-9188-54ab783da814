@use '../../../../scss/variables' as *;
@use '../../../../scss/mixins' as *;

.container {
  @include flex-column;
  gap: $spacing-6;
}

.header {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: stretch;
  }
}

.headerContent {
  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0 0 $spacing-2 0;
  }

  p {
    color: $gray-600;
    font-size: $font-size-sm;
    margin: 0;
    line-height: $line-height-relaxed;
  }
}

.loading {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-12;
  color: $gray-600;

  p {
    font-size: $font-size-sm;
    margin: 0;
  }
}

.selectedCard {
  background: $primary-25;
  border: 1px solid $primary-200;
}

.selectedHeader {
  margin-bottom: $spacing-4;

  h4 {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $primary-900;
    margin: 0 0 $spacing-1 0;
  }

  p {
    color: $primary-700;
    font-size: $font-size-sm;
    margin: 0;
  }
}

.selectedAttributes {
  @include flex-start;
  flex-wrap: wrap;
  gap: $spacing-2;
}

.selectedAttribute {
  @include flex-between;
  align-items: center;
  gap: $spacing-2;
  background: white;
  border: 1px solid $primary-300;
  border-radius: $border-radius;
  padding: $spacing-2 $spacing-3;
}

.attributeInfo {
  @include flex-start;
  align-items: center;
  gap: $spacing-2;
  color: $primary-700;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
}

.removeButton {
  color: $gray-500;
  padding: $spacing-1;

  &:hover {
    color: $error-600;
    background: $error-50;
  }
}

.attributesCard {
  background: white;
}

.attributesHeader {
  margin-bottom: $spacing-4;

  h4 {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0 0 $spacing-1 0;
  }

  p {
    color: $gray-600;
    font-size: $font-size-sm;
    margin: 0;
  }
}

.attributesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: $spacing-4;

  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.attributeCard {
  transition: all 0.2s ease-in-out;
  border: 1px solid $gray-200;

  &.selected {
    border-color: $primary-500;
    background: $primary-25;
    box-shadow: 0 0 0 1px $primary-500;
  }

  &:hover:not(.selected) {
    border-color: $gray-300;
    box-shadow: $shadow-sm;
  }
}

.attributeContent {
  @include flex-column;
  gap: $spacing-4;
}

.attributeHeader {
  @include flex-start;
  gap: $spacing-3;
  position: relative;
}

.attributeIcon {
  @include flex-center;
  width: 40px;
  height: 40px;
  background: $gray-100;
  color: $gray-600;
  border-radius: $border-radius;
  font-size: $font-size-base;

  .selected & {
    background: $primary-100;
    color: $primary-600;
  }
}

.attributeInfo {
  flex: 1;
}

.attributeName {
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: $gray-900;
  margin: 0 0 $spacing-1 0;

  .selected & {
    color: $primary-900;
  }
}

.attributeStats {
  @include flex-start;
  gap: $spacing-2;
}

.selectedBadge {
  @include flex-center;
  width: 24px;
  height: 24px;
  background: $primary-500;
  color: white;
  border-radius: $border-radius-full;
  font-size: $font-size-sm;
  position: absolute;
  top: -$spacing-1;
  right: -$spacing-1;
}

.selectButton {
  width: 100%;
}

.emptyState {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-12;
  color: $gray-500;
  grid-column: 1 / -1;

  svg {
    font-size: $font-size-4xl;
    color: $gray-300;
  }

  h4 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-700;
    margin: 0;
  }

  p {
    font-size: $font-size-sm;
    margin: 0;
    text-align: center;
  }
}

// Modal form styles
.form {
  @include flex-column;
  gap: $spacing-4;
}

.formGroup {
  @include flex-column;
  gap: $spacing-2;
}

.label {
  font-weight: $font-weight-medium;
  color: $gray-700;
  font-size: $font-size-sm;
}

.formActions {
  @include flex-end;
  gap: $spacing-3;
  margin-top: $spacing-2;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
}
