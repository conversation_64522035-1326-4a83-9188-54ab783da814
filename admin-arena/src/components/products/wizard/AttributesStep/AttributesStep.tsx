// Attributes selection step in product wizard
// Allows selecting attributes that will be used for product variants

import React, { useState } from 'react'
import { FiPlus, FiTool, FiCheck, FiX } from 'react-icons/fi'
import { useAttributes, useCreateAttribute } from '../../../../hooks/use-products'
import { Card } from '../../../ui/Card'
import { Button } from '../../../ui/Button'
import { Input } from '../../../ui/Input'
import { Modal } from '../../../ui/Modal'
import { Badge } from '../../../ui/Badge'
import { LoadingSpinner } from '../../../ui/LoadingSpinner'
import { WizardData } from '../../../../pages/products/ProductWizardPage'
import { Attribute } from '../../../../types/api-types'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import styles from './AttributesStep.module.scss'

interface AttributesStepProps {
  data: WizardData
  onUpdate: (data: Partial<WizardData>) => void
}

const attributeSchema = z.object({
  title: z.string().min(1, 'Attribute name is required'),
})

type AttributeFormData = z.infer<typeof attributeSchema>

export const AttributesStep: React.FC<AttributesStepProps> = ({ data, onUpdate }) => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [selectedAttributes, setSelectedAttributes] = useState<Attribute[]>(
    data.attributes || []
  )

  const { data: attributes, isLoading, refetch } = useAttributes()
  const createAttributeMutation = useCreateAttribute()

  const {
    register,
    handleSubmit,
    reset: resetForm,
    formState: { errors, isSubmitting },
  } = useForm<AttributeFormData>({
    resolver: zodResolver(attributeSchema),
  })

  const handleAttributeToggle = (attribute: Attribute) => {
    const isSelected = selectedAttributes.some(attr => attr.id === attribute.id)
    
    let newSelectedAttributes: Attribute[]
    if (isSelected) {
      newSelectedAttributes = selectedAttributes.filter(attr => attr.id !== attribute.id)
    } else {
      newSelectedAttributes = [...selectedAttributes, attribute]
    }
    
    setSelectedAttributes(newSelectedAttributes)
    onUpdate({
      attributes: newSelectedAttributes.map(attr => ({
        id: attr.id,
        name: attr.title,
        values: [] // Will be populated in next steps
      }))
    })
  }

  const handleCreateAttribute = async (formData: AttributeFormData) => {
    try {
      const newAttribute = await createAttributeMutation.mutateAsync(formData)
      setIsCreateModalOpen(false)
      resetForm()
      refetch()
      
      // Auto-select the newly created attribute
      if (newAttribute) {
        handleAttributeToggle(newAttribute as Attribute)
      }
    } catch (error) {
      // Error is handled by the mutation
    }
  }

  if (isLoading) {
    return (
      <div className={styles.loading}>
        <LoadingSpinner size="lg" />
        <p>Loading attributes...</p>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h3>Select Product Attributes</h3>
          <p>Choose the attributes that will be used to create product variants (e.g., Color, Size, Material).</p>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsCreateModalOpen(true)}
        >
          <FiPlus />
          New Attribute
        </Button>
      </div>

      {selectedAttributes.length > 0 && (
        <Card className={styles.selectedCard}>
          <div className={styles.selectedHeader}>
            <h4>Selected Attributes ({selectedAttributes.length})</h4>
            <p>These attributes will be used to create product variants</p>
          </div>
          
          <div className={styles.selectedAttributes}>
            {selectedAttributes.map((attribute) => (
              <div key={attribute.id} className={styles.selectedAttribute}>
                <div className={styles.attributeInfo}>
                  <FiTool />
                  <span>{attribute.title}</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleAttributeToggle(attribute)}
                  className={styles.removeButton}
                >
                  <FiX />
                </Button>
              </div>
            ))}
          </div>
        </Card>
      )}

      <Card className={styles.attributesCard}>
        <div className={styles.attributesHeader}>
          <h4>Available Attributes</h4>
          <p>Click on attributes to select them for your product</p>
        </div>

        <div className={styles.attributesGrid}>
          {attributes && attributes.length > 0 ? (
            attributes.map((attribute) => {
              const isSelected = selectedAttributes.some(attr => attr.id === attribute.id)

              return (
                <Card
                  key={attribute.id}
                  className={`${styles.attributeCard} ${isSelected ? styles.selected : ''}`}
                  hover={!isSelected}
                >
                  <div className={styles.attributeContent}>
                    <div className={styles.attributeHeader}>
                      <div className={styles.attributeIcon}>
                        <FiTool />
                      </div>
                      <div className={styles.attributeInfo}>
                        <h4 className={styles.attributeName}>{attribute.title}</h4>
                        <div className={styles.attributeStats}>
                          <Badge variant="secondary" size="sm">
                            {attribute.values_count || 0} values
                          </Badge>
                        </div>
                      </div>
                      {isSelected && (
                        <div className={styles.selectedBadge}>
                          <FiCheck />
                        </div>
                      )}
                    </div>

                    <Button
                      variant={isSelected ? "primary" : "outline"}
                      size="sm"
                      onClick={() => handleAttributeToggle(attribute)}
                      className={styles.selectButton}
                    >
                      {isSelected ? 'Selected' : 'Select'}
                    </Button>
                  </div>
                </Card>
              )
            })
          ) : (
            <div className={styles.emptyState}>
              <FiTool />
              <h4>No attributes found</h4>
              <p>Create your first attribute to get started</p>
              <Button
                variant="primary"
                onClick={() => setIsCreateModalOpen(true)}
              >
                <FiPlus />
                Create Attribute
              </Button>
            </div>
          )}
        </div>
      </Card>

      {/* Create Attribute Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false)
          resetForm()
        }}
        title="Create New Attribute"
      >
        <form onSubmit={handleSubmit(handleCreateAttribute)} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="title" className={styles.label}>
              Attribute Name *
            </label>
            <Input
              id="title"
              {...register('title')}
              placeholder="Enter attribute name (e.g., Color, Size, Material)"
              error={errors.title?.message}
            />
          </div>

          <div className={styles.formActions}>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsCreateModalOpen(false)
                resetForm()
              }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
            >
              Create Attribute
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  )
}
