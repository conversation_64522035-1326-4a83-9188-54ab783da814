// Variant form modal for creating and editing product variants
// Handles SKU, pricing, inventory, and attribute values

import React, { useEffect } from 'react'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiDollarSign, FiPackage, FiTag } from 'react-icons/fi'
import { Modal } from '../../ui/Modal'
import { Button } from '../../ui/Button'
import { Input } from '../../ui/Input'
import { Switch } from '../../ui/Switch'
import { Card } from '../../ui/Card'
import { Badge } from '../../ui/Badge'
import { useCreateVariant, useUpdateVariant } from '../../../hooks/use-products'
import { ProductVariant } from '../../../types/api-types'
import styles from './VariantFormModal.module.scss'

interface VariantFormModalProps {
  isOpen: boolean
  onClose: () => void
  productId: number
  variant?: ProductVariant
  mode: 'create' | 'edit'
  onSuccess: () => void
}

const variantSchema = z.object({
  sku: z.string().min(1, 'SKU is required'),
  price: z.number().min(0, 'Price must be positive'),
  compare_at_price: z.number().min(0, 'Compare price must be positive').optional(),
  quantity_available: z.number().min(0, 'Quantity must be positive'),
  is_active: z.boolean(),
  weight: z.number().min(0, 'Weight must be positive').optional(),
  dimensions: z.string().optional(),
})

type VariantFormData = z.infer<typeof variantSchema>

export const VariantFormModal: React.FC<VariantFormModalProps> = ({
  isOpen,
  onClose,
  productId,
  variant,
  mode,
  onSuccess,
}) => {
  const createVariantMutation = useCreateVariant()
  const updateVariantMutation = useUpdateVariant()

  const {
    register,
    handleSubmit,
    reset,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<VariantFormData>({
    resolver: zodResolver(variantSchema),
    defaultValues: {
      sku: '',
      price: 0,
      compare_at_price: undefined,
      quantity_available: 0,
      is_active: true,
      weight: undefined,
      dimensions: '',
    },
  })

  const watchedPrice = watch('price')
  const watchedComparePrice = watch('compare_at_price')

  // Reset form when modal opens/closes or variant changes
  useEffect(() => {
    if (isOpen) {
      if (variant && mode === 'edit') {
        reset({
          sku: variant.sku || '',
          price: variant.price || 0,
          compare_at_price: variant.compare_at_price || undefined,
          quantity_available: variant.quantity_available || 0,
          is_active: variant.is_active ?? true,
          weight: variant.weight || undefined,
          dimensions: variant.dimensions || '',
        })
      } else {
        reset({
          sku: '',
          price: 0,
          compare_at_price: undefined,
          quantity_available: 0,
          is_active: true,
          weight: undefined,
          dimensions: '',
        })
      }
    }
  }, [isOpen, variant, mode, reset])

  const onSubmit = async (data: VariantFormData) => {
    try {
      const variantData = {
        ...data,
        product: productId,
        compare_at_price: data.compare_at_price || null,
        weight: data.weight || null,
        dimensions: data.dimensions || null,
      }

      if (mode === 'create') {
        await createVariantMutation.mutateAsync(variantData)
      } else if (variant) {
        await updateVariantMutation.mutateAsync({
          id: variant.id,
          data: variantData,
        })
      }

      onSuccess()
      onClose()
    } catch (error) {
      // Error handling is done by the mutations
    }
  }

  const calculateDiscount = () => {
    if (watchedComparePrice && watchedPrice && watchedComparePrice > watchedPrice) {
      const discount = ((watchedComparePrice - watchedPrice) / watchedComparePrice) * 100
      return Math.round(discount)
    }
    return 0
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Create Product Variant' : 'Edit Product Variant'}
      size="lg"
    >
      <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
        <div className={styles.formContent}>
          {/* Basic Information */}
          <Card className={styles.section}>
            <div className={styles.sectionHeader}>
              <div className={styles.sectionIcon}>
                <FiTag />
              </div>
              <div>
                <h3>Basic Information</h3>
                <p>SKU and identification details</p>
              </div>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="sku" className={styles.label}>
                SKU (Stock Keeping Unit) *
              </label>
              <Input
                id="sku"
                {...register('sku')}
                placeholder="Enter unique SKU"
                error={errors.sku?.message}
              />
              <span className={styles.helpText}>
                Must be unique across all variants
              </span>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="dimensions" className={styles.label}>
                Dimensions
              </label>
              <Input
                id="dimensions"
                {...register('dimensions')}
                placeholder="e.g., 10 x 5 x 2 cm"
                error={errors.dimensions?.message}
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="weight" className={styles.label}>
                Weight (kg)
              </label>
              <Input
                id="weight"
                type="number"
                step="0.01"
                {...register('weight', { valueAsNumber: true })}
                placeholder="0.00"
                error={errors.weight?.message}
              />
            </div>
          </Card>

          {/* Pricing */}
          <Card className={styles.section}>
            <div className={styles.sectionHeader}>
              <div className={styles.sectionIcon}>
                <FiDollarSign />
              </div>
              <div>
                <h3>Pricing</h3>
                <p>Set pricing and discount information</p>
              </div>
            </div>

            <div className={styles.formRow}>
              <div className={styles.formGroup}>
                <label htmlFor="price" className={styles.label}>
                  Price *
                </label>
                <div className={styles.priceInput}>
                  <FiDollarSign className={styles.priceIcon} />
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    {...register('price', { valueAsNumber: true })}
                    placeholder="0.00"
                    error={errors.price?.message}
                  />
                </div>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="compare_at_price" className={styles.label}>
                  Compare at Price
                </label>
                <div className={styles.priceInput}>
                  <FiDollarSign className={styles.priceIcon} />
                  <Input
                    id="compare_at_price"
                    type="number"
                    step="0.01"
                    {...register('compare_at_price', { valueAsNumber: true })}
                    placeholder="0.00"
                    error={errors.compare_at_price?.message}
                  />
                </div>
              </div>
            </div>

            {calculateDiscount() > 0 && (
              <div className={styles.discountInfo}>
                <Badge variant="success">
                  {calculateDiscount()}% discount
                </Badge>
                <span className={styles.discountText}>
                  Customers will see this as a discount
                </span>
              </div>
            )}
          </Card>

          {/* Inventory */}
          <Card className={styles.section}>
            <div className={styles.sectionHeader}>
              <div className={styles.sectionIcon}>
                <FiPackage />
              </div>
              <div>
                <h3>Inventory</h3>
                <p>Stock levels and availability</p>
              </div>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="quantity_available" className={styles.label}>
                Quantity Available *
              </label>
              <Input
                id="quantity_available"
                type="number"
                {...register('quantity_available', { valueAsNumber: true })}
                placeholder="0"
                error={errors.quantity_available?.message}
              />
            </div>

            <div className={styles.switchGroup}>
              <div className={styles.switchInfo}>
                <label htmlFor="is_active" className={styles.label}>
                  Active Variant
                </label>
                <span className={styles.helpText}>
                  Active variants are available for purchase
                </span>
              </div>
              <Controller
                name="is_active"
                control={control}
                render={({ field }) => (
                  <Switch
                    id="is_active"
                    checked={field.value}
                    onChange={field.onChange}
                  />
                )}
              />
            </div>
          </Card>
        </div>

        <div className={styles.formActions}>
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            className={`${styles.submitButton} ${styles.primaryButton}`}
            loading={isSubmitting}
          >
            {isSubmitting
              ? (mode === 'create' ? 'Creating Variant...' : 'Updating Variant...')
              : (mode === 'create' ? 'Create Variant' : 'Update Variant')}
          </Button>
        </div>
      </form>
    </Modal>
  )
}
