@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: $spacing-4;
}

.modal {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-xl;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modalEnter 0.2s ease-out;

  &:focus {
    outline: none;
  }
}

@keyframes modalEnter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Size variants
.sm {
  width: 100%;
  max-width: 400px;
}

.md {
  width: 100%;
  max-width: 500px;
}

.lg {
  width: 100%;
  max-width: 800px;
}

.xl {
  width: 100%;
  max-width: 1200px;
}

.full {
  width: 95vw;
  height: 95vh;
  max-width: none;
  max-height: none;
}

// Modal sections
.header {
  @include flex-between;
  padding: $spacing-6 $spacing-6 $spacing-4 $spacing-6;
  border-bottom: 1px solid $gray-200;
  flex-shrink: 0;
}

.title {
  margin: 0;
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $gray-900;
}

.closeButton {
  margin-left: $spacing-4;
  padding: $spacing-2;
  
  svg {
    width: 20px;
    height: 20px;
  }
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: $spacing-6;
}

// When using compound components
.modalHeader {
  padding: $spacing-6 $spacing-6 $spacing-4 $spacing-6;
  border-bottom: 1px solid $gray-200;
}

.modalBody {
  flex: 1;
  overflow-y: auto;
  padding: $spacing-6;
}

.modalFooter {
  padding: $spacing-4 $spacing-6 $spacing-6 $spacing-6;
  border-top: 1px solid $gray-200;
  @include flex-start;
  gap: $spacing-3;
  flex-shrink: 0;
}

// Responsive adjustments
@include mobile-only {
  .backdrop {
    padding: $spacing-2;
  }
  
  .modal {
    max-height: 95vh;
  }
  
  .sm,
  .md,
  .lg,
  .xl {
    width: 100%;
    max-width: none;
  }
  
  .header,
  .content,
  .modalHeader,
  .modalBody,
  .modalFooter {
    padding-left: $spacing-4;
    padding-right: $spacing-4;
  }
}
