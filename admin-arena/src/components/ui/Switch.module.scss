@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.container {
  @include flex-start;
  align-items: center;
  gap: $spacing-3;
}

.input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;

  &:checked + .switch .slider {
    background-color: $primary-500;
    
    &::before {
      transform: translateX(100%);
    }
  }

  &:focus + .switch {
    box-shadow: 0 0 0 3px rgba($primary-500, 0.1);
  }

  &:disabled + .switch {
    opacity: 0.5;
    cursor: not-allowed;
    
    .slider {
      cursor: not-allowed;
    }
  }
}

.switch {
  position: relative;
  display: inline-block;
  cursor: pointer;
  border-radius: $border-radius-full;
  transition: box-shadow 0.2s ease-in-out;

  &.sm {
    width: 32px;
    height: 18px;
  }

  &.md {
    width: 44px;
    height: 24px;
  }

  &.lg {
    width: 56px;
    height: 30px;
  }
}

.slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: $gray-300;
  border-radius: $border-radius-full;
  transition: background-color 0.2s ease-in-out;
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    background-color: white;
    border-radius: $border-radius-full;
    transition: transform 0.2s ease-in-out;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .sm & {
    &::before {
      width: 14px;
      height: 14px;
    }
  }

  .md & {
    &::before {
      width: 20px;
      height: 20px;
    }
  }

  .lg & {
    &::before {
      width: 26px;
      height: 26px;
    }
  }
}

.label {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-700;
  cursor: pointer;
  user-select: none;
}
