// DataTable component styles
// Professional table design with interactive features

@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.tableContainer {
  @include card;
  overflow: hidden;
}

.bulkActions {
  @include flex-between;
  padding: $spacing-4;
  background-color: $primary-50;
  border-bottom: 1px solid $primary-200;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.selectedCount {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $primary-700;
}

.bulkActionsContent {
  @include flex-start;
  gap: $spacing-2;

  @include mobile-only {
    width: 100%;

    button {
      flex: 1;
    }
  }
}

.tableWrapper {
  overflow: auto;

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: $gray-100;
  }

  &::-webkit-scrollbar-thumb {
    background: $gray-300;
    border-radius: $border-radius;

    &:hover {
      background: $gray-400;
    }
  }
}

.table {
  @include table-base;

  &.stickyHeader {
    thead th {
      position: sticky;
      top: 0;
      z-index: $z-10;
      background-color: $gray-50;
      box-shadow: 0 1px 0 $gray-200;
    }
  }
}

.thead {
  background-color: $gray-50;
}

.th {
  font-weight: $font-weight-semibold;
  font-size: $font-size-xs;
  color: $gray-700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: $spacing-3 $spacing-4;
  border-bottom: 1px solid $gray-200;

  &.sortable {
    cursor: pointer;
    user-select: none;
    transition: $transition-colors;

    &:hover {
      background-color: $gray-100;
    }
  }
}

.thContent {
  @include flex-between;
  gap: $spacing-2;
}

.sortIcon {
  @include flex-center;
  color: $gray-500;

  svg {
    width: $spacing-3;
    height: $spacing-3;
  }
}

.tbody {
  tr {
    transition: $transition-colors;

    &:hover {
      background-color: $gray-50;
    }

    &.selected {
      background-color: $primary-50;

      &:hover {
        background-color: $primary-100;
      }
    }

    &.clickable {
      cursor: pointer;
    }
  }
}

.tr {
  &.hovered {
    background-color: $gray-50;
  }
}

.td {
  padding: $spacing-3 $spacing-4;
  font-size: $font-size-sm;
  color: $gray-900;
  border-bottom: 1px solid $gray-200;
  vertical-align: middle;

  // Prevent text overflow in cells
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.checkboxColumn {
  width: $spacing-12;
  text-align: center;
  padding: $spacing-3 $spacing-2;
}

.checkbox {
  width: $spacing-4;
  height: $spacing-4;
  border-radius: $border-radius-sm;
  border: 1px solid $gray-300;
  cursor: pointer;

  &:checked {
    background-color: $primary-600;
    border-color: $primary-600;
  }

  &:focus {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
  }
}

.actionsColumn {
  width: $spacing-12;
  text-align: center;
  padding: $spacing-2;
}

.loadingContainer {
  @include flex-column-center;
  padding: $spacing-16;
  gap: $spacing-4;
  color: $gray-600;
}

.loadingSpinner {
  width: $spacing-8;
  height: $spacing-8;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.emptyState {
  @include flex-center;
  padding: $spacing-16;
  color: $gray-500;
}

.emptyMessage {
  font-size: $font-size-base;
  margin: 0;
}

// Responsive adjustments
@include mobile-only {
  .table {
    font-size: $font-size-xs;
  }

  .th,
  .td {
    padding: $spacing-2 $spacing-3;
  }

  .td {
    max-width: 120px;
  }
}

// Print styles
@media print {

  .bulkActions,
  .actionsColumn,
  .checkboxColumn {
    display: none;
  }

  .table {
    box-shadow: none;
    border: 1px solid $gray-300;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .table {
    border: 2px solid $gray-900;
  }

  .th,
  .td {
    border-bottom: 1px solid $gray-900;
  }

  .tr.selected {
    background-color: $primary-200;
    border: 2px solid $primary-600;
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {

  .tr,
  .th.sortable,
  .loadingSpinner {
    transition: none;
    animation: none;
  }
}