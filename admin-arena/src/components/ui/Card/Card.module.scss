@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.card {
  @include card;
  transition: all 0.2s ease-in-out;

  &.hover:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }
}

// Padding variants
.padding-none {
  padding: 0;
}

.padding-sm {
  padding: $spacing-3;
}

.padding-md {
  padding: $spacing-4;
}

.padding-lg {
  padding: $spacing-6;
}

// Shadow variants
.shadow-none {
  box-shadow: none;
}

.shadow-sm {
  box-shadow: $shadow-sm;
}

.shadow-md {
  box-shadow: $shadow-md;
}

.shadow-lg {
  box-shadow: $shadow-lg;
}

// Border variant
.border {
  border: 1px solid $gray-200;
}

// Card sections
.header {
  padding: $spacing-4 $spacing-4 $spacing-3 $spacing-4;
  border-bottom: 1px solid $gray-200;
  
  &:last-child {
    border-bottom: none;
    padding-bottom: $spacing-4;
  }
}

.body {
  padding: $spacing-4;
  flex: 1;
}

.footer {
  padding: $spacing-3 $spacing-4 $spacing-4 $spacing-4;
  border-top: 1px solid $gray-200;
  background-color: $gray-50;
  border-radius: 0 0 $border-radius-lg $border-radius-lg;
  
  &:first-child {
    border-top: none;
    background-color: transparent;
    padding-top: $spacing-4;
  }
}

// When card has no padding, adjust section padding
.padding-none {
  .header {
    padding: $spacing-4;
    border-bottom: 1px solid $gray-200;
  }
  
  .body {
    padding: $spacing-4;
  }
  
  .footer {
    padding: $spacing-4;
    border-top: 1px solid $gray-200;
  }
}
