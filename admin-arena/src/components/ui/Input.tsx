// Input component with validation and accessibility features
// Supports icons, error states, and various input types

import React, { forwardRef } from 'react';
import styles from './Input.module.scss';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  variant?: 'default' | 'filled';
}

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  fullWidth = true,
  variant = 'default',
  className,
  id,
  ...props
}, ref) => {
  // Generate unique ID if not provided
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  
  const inputClasses = [
    styles.input,
    styles[variant],
    leftIcon && styles.hasLeftIcon,
    rightIcon && styles.hasRightIcon,
    error && styles.error,
    fullWidth && styles.fullWidth,
    className
  ].filter(Boolean).join(' ');

  const wrapperClasses = [
    styles.inputGroup,
    fullWidth && styles.fullWidth
  ].filter(Boolean).join(' ');

  return (
    <div className={wrapperClasses}>
      {label && (
        <label className={styles.label} htmlFor={inputId}>
          {label}
          {props.required && <span className={styles.required} aria-label="required">*</span>}
        </label>
      )}
      
      <div className={styles.inputWrapper}>
        {leftIcon && (
          <div className={styles.leftIcon} aria-hidden="true">
            {leftIcon}
          </div>
        )}
        
        <input
          ref={ref}
          id={inputId}
          className={inputClasses}
          aria-invalid={!!error}
          aria-describedby={
            error ? `${inputId}-error` : 
            helperText ? `${inputId}-helper` : 
            undefined
          }
          {...props}
        />
        
        {rightIcon && (
          <div className={styles.rightIcon} aria-hidden="true">
            {rightIcon}
          </div>
        )}
      </div>
      
      {error && (
        <span 
          id={`${inputId}-error`}
          className={styles.errorText} 
          role="alert"
          aria-live="polite"
        >
          {error}
        </span>
      )}
      
      {helperText && !error && (
        <span 
          id={`${inputId}-helper`}
          className={styles.helperText}
        >
          {helperText}
        </span>
      )}
    </div>
  );
});

Input.displayName = 'Input';

// Specialized input components
export const EmailInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => <Input ref={ref} type="email" {...props} />
);

export const PasswordInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => <Input ref={ref} type="password" {...props} />
);

export const NumberInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => <Input ref={ref} type="number" {...props} />
);

export const SearchInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => <Input ref={ref} type="search" {...props} />
);

export const TelInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => <Input ref={ref} type="tel" {...props} />
);

export const UrlInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => <Input ref={ref} type="url" {...props} />
);

EmailInput.displayName = 'EmailInput';
PasswordInput.displayName = 'PasswordInput';
NumberInput.displayName = 'NumberInput';
SearchInput.displayName = 'SearchInput';
TelInput.displayName = 'TelInput';
UrlInput.displayName = 'UrlInput';
