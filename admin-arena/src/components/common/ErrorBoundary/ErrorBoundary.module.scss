@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  @include flex-center;
  min-height: 400px;
  padding: $spacing-4;
}

.card {
  max-width: 600px;
  width: 100%;
}

.content {
  @include flex-column-center;
  text-align: center;
  gap: $spacing-6;
}

.icon {
  @include flex-center;
  width: 80px;
  height: 80px;
  background-color: $red-100;
  border-radius: 50%;
  color: $red-600;
  
  svg {
    width: 40px;
    height: 40px;
  }
}

.title {
  margin: 0;
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
}

.message {
  margin: 0;
  font-size: $font-size-base;
  color: $gray-600;
  line-height: 1.6;
  max-width: 500px;
}

.errorDetails {
  width: 100%;
  max-width: 500px;
  text-align: left;
  border: 1px solid $gray-300;
  border-radius: $border-radius;
  background-color: $gray-50;
}

.errorSummary {
  padding: $spacing-3 $spacing-4;
  font-weight: $font-weight-medium;
  color: $gray-700;
  cursor: pointer;
  border-bottom: 1px solid $gray-300;
  
  &:hover {
    background-color: $gray-100;
  }
}

.errorContent {
  padding: $spacing-4;
  
  h3 {
    margin: 0 0 $spacing-2 0;
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    color: $gray-800;
  }
  
  h3:not(:first-child) {
    margin-top: $spacing-4;
  }
}

.errorText {
  background-color: $gray-900;
  color: $gray-100;
  padding: $spacing-3;
  border-radius: $border-radius;
  font-size: $font-size-xs;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
}

.actions {
  @include flex-center;
  gap: $spacing-3;
  flex-wrap: wrap;
}

.retryButton,
.homeButton {
  @include flex-center;
  gap: $spacing-2;
  min-width: 120px;
}

@include mobile-only {
  .actions {
    flex-direction: column;
    width: 100%;
  }
  
  .retryButton,
  .homeButton {
    width: 100%;
    justify-content: center;
  }
  
  .errorDetails {
    max-width: none;
  }
}
