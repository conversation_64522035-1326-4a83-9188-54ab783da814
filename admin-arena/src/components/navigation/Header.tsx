// Header component with breadcrumbs and user menu
// Responsive header for admin dashboard

import React, { useState } from 'react';
import { 
  FiMenu, 
  FiBell, 
  FiUser, 
  FiSettings, 
  FiLogOut,
  FiChevronDown,
  FiChevronRight,
  FiHome
} from 'react-icons/fi';
import { Link } from '@tanstack/react-router';
import { useAuth, useLogout } from '../../hooks/use-auth';
import { useSidebar, usePageState, useNotifications } from '../../stores/ui-store';
import { Button } from '../ui/Button';
import styles from './Header.module.scss';

export const Header: React.FC = () => {
  const { user } = useAuth();
  const { toggleMobileSidebar } = useSidebar();
  const { pageTitle, breadcrumbs } = usePageState();
  const { notifications } = useNotifications();
  const logoutMutation = useLogout();
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  const unreadNotifications = notifications.filter(n => n.type === 'info' || n.type === 'warning').length;

  const handleLogout = async () => {
    try {
      await logoutMutation.mutateAsync();
      setUserMenuOpen(false);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header className={styles.header}>
      <div className={styles.left}>
        <button
          className={styles.mobileMenuButton}
          onClick={toggleMobileSidebar}
          aria-label="Open sidebar"
        >
          <FiMenu />
        </button>

        <div className={styles.breadcrumbs}>
          <nav aria-label="Breadcrumb">
            <ol className={styles.breadcrumbList}>
              <li className={styles.breadcrumbItem}>
                <Link to="/" className={styles.breadcrumbLink}>
                  <FiHome />
                  <span className={styles.breadcrumbText}>Dashboard</span>
                </Link>
              </li>
              
              {breadcrumbs.map((crumb, index) => (
                <li key={index} className={styles.breadcrumbItem}>
                  <FiChevronRight className={styles.breadcrumbSeparator} />
                  {crumb.path ? (
                    <Link to={crumb.path} className={styles.breadcrumbLink}>
                      {crumb.label}
                    </Link>
                  ) : (
                    <span className={styles.breadcrumbCurrent} aria-current="page">
                      {crumb.label}
                    </span>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        </div>
      </div>

      <div className={styles.right}>
        {/* Notifications */}
        <button className={styles.notificationButton} aria-label="Notifications">
          <FiBell />
          {unreadNotifications > 0 && (
            <span className={styles.notificationBadge}>
              {unreadNotifications > 9 ? '9+' : unreadNotifications}
            </span>
          )}
        </button>

        {/* User Menu */}
        <div className={styles.userMenu}>
          <button
            className={styles.userMenuButton}
            onClick={() => setUserMenuOpen(!userMenuOpen)}
            aria-expanded={userMenuOpen}
            aria-haspopup="true"
          >
            <div className={styles.userAvatar}>
              {user?.email.charAt(0).toUpperCase()}
            </div>
            <div className={styles.userInfo}>
              <span className={styles.userName}>
                {user?.staff_profile?.full_name || user?.email}
              </span>
              <span className={styles.userRole}>
                {user?.staff_profile?.position_title || 'Admin'}
              </span>
            </div>
            <FiChevronDown className={styles.chevron} />
          </button>

          {userMenuOpen && (
            <div className={styles.userMenuDropdown}>
              <div className={styles.userMenuHeader}>
                <div className={styles.userMenuAvatar}>
                  {user?.email.charAt(0).toUpperCase()}
                </div>
                <div>
                  <div className={styles.userMenuName}>
                    {user?.staff_profile?.full_name || user?.email}
                  </div>
                  <div className={styles.userMenuEmail}>{user?.email}</div>
                </div>
              </div>

              <div className={styles.userMenuDivider} />

              <div className={styles.userMenuItems}>
                <Link
                  to="/settings/profile"
                  className={styles.userMenuItem}
                  onClick={() => setUserMenuOpen(false)}
                >
                  <FiUser />
                  <span>Profile</span>
                </Link>

                <Link
                  to="/settings"
                  className={styles.userMenuItem}
                  onClick={() => setUserMenuOpen(false)}
                >
                  <FiSettings />
                  <span>Settings</span>
                </Link>
              </div>

              <div className={styles.userMenuDivider} />

              <button
                className={styles.userMenuItem}
                onClick={handleLogout}
                disabled={logoutMutation.isPending}
              >
                <FiLogOut />
                <span>{logoutMutation.isPending ? 'Signing out...' : 'Sign out'}</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close user menu */}
      {userMenuOpen && (
        <div
          className={styles.overlay}
          onClick={() => setUserMenuOpen(false)}
        />
      )}
    </header>
  );
};
