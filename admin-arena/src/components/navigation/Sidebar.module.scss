// Sidebar navigation styles
// Responsive sidebar with smooth transitions

@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: $sidebar-width;
  background: white;
  border-right: 1px solid $gray-200;
  box-shadow: $shadow-sm;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease, transform 0.3s ease;
  z-index: $z-fixed;

  &.collapsed {
    width: $sidebar-collapsed-width;
  }

  @include mobile-only {
    transform: translateX(-100%);

    &.mobileOpen {
      transform: translateX(0);
    }
  }
}

.mobileOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: $z-modal-backdrop;

  @include desktop-only {
    display: none;
  }
}

.header {
  @include flex-between;
  padding: $spacing-4;
  border-bottom: 1px solid $gray-200;
  min-height: $header-height;
}

.logo {
  flex: 1;
  min-width: 0;
}

.logoFull {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $primary-600;
  @include truncate;
}

.logoCollapsed {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $primary-600;
  text-align: center;
  display: block;
}

.toggleButton {
  @include flex-center;
  width: $spacing-8;
  height: $spacing-8;
  background: none;
  border: none;
  border-radius: $border-radius;
  color: $gray-600;
  cursor: pointer;
  transition: $transition-colors;

  &:hover {
    background-color: $gray-100;
    color: $gray-900;
  }

  &:focus {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
  }

  svg {
    width: $spacing-5;
    height: $spacing-5;
  }

  @include mobile-only {
    display: none;
  }
}

.mobileCloseButton {
  @include flex-center;
  width: $spacing-8;
  height: $spacing-8;
  background: none;
  border: none;
  border-radius: $border-radius;
  color: $gray-600;
  cursor: pointer;
  transition: $transition-colors;

  &:hover {
    background-color: $gray-100;
    color: $gray-900;
  }

  svg {
    width: $spacing-5;
    height: $spacing-5;
  }

  @include desktop-only {
    display: none;
  }
}

.navigation {
  flex: 1;
  padding: $spacing-4 0;
  overflow-y: auto;
}

.navigationList {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: $spacing-1;
}

.navigationItem {
  padding: 0 $spacing-4;
}

.navigationParent {
  position: relative;
}

.subMenu {
  list-style: none;
  margin: 0;
  padding: 0;
  padding-left: $spacing-8;
  margin-top: $spacing-1;
  border-left: 1px solid $gray-200;
  margin-left: $spacing-6;
}

.subNavigationItem {
  margin-bottom: $spacing-1;
}

.subNavigationLink {
  @include flex-start;
  gap: $spacing-2;
  padding: $spacing-2 $spacing-3;
  border-radius: $border-radius;
  color: $gray-600;
  text-decoration: none;
  transition: $transition-colors;
  width: 100%;
  font-size: $font-size-sm;

  &:hover {
    background-color: $gray-100;
    color: $gray-900;
  }

  &:focus {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
  }

  &.active {
    background-color: $primary-100;
    color: $primary-700;

    .subIcon {
      color: $primary-600;
    }
  }
}

.subIcon {
  width: $spacing-4;
  height: $spacing-4;
  flex-shrink: 0;
  color: $gray-400;
  transition: $transition-colors;
}

.subLabel {
  font-weight: $font-weight-medium;
}

.navigationLink {
  @include flex-start;
  gap: $spacing-3;
  padding: $spacing-3;
  border-radius: $border-radius;
  color: $gray-700;
  text-decoration: none;
  transition: $transition-colors;
  width: 100%;
  border: none;
  background: none;
  cursor: pointer;

  &:hover {
    background-color: $gray-100;
    color: $gray-900;
  }

  &:focus {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
  }

  &.active {
    background-color: $primary-50;
    color: $primary-700;

    .icon {
      color: $primary-600;
    }
  }

  .collapsed & {
    justify-content: center;
    padding: $spacing-3 $spacing-2;
  }
}

.hasActiveChild {
  .navigationLink {
    background-color: $primary-25;
    color: $primary-600;
  }
}

.expandIcon {
  margin-left: auto;
  @include flex-center;

  svg {
    width: $spacing-4;
    height: $spacing-4;
    transition: transform 0.2s ease;
  }
}

.icon {
  width: $spacing-5;
  height: $spacing-5;
  flex-shrink: 0;
  color: $gray-500;
  transition: $transition-colors;
}

.label {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  @include truncate;

  .collapsed & {
    display: none;
  }
}

.footer {
  padding: $spacing-4;
  border-top: 1px solid $gray-200;

  .collapsed & {
    padding: $spacing-2;
  }
}

.userInfo {
  @include flex-start;
  gap: $spacing-3;

  .collapsed & {
    justify-content: center;
  }
}

.userAvatar {
  @include flex-center;
  width: $spacing-8;
  height: $spacing-8;
  background: linear-gradient(135deg, $primary-500, $primary-600);
  color: white;
  border-radius: $border-radius-full;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  flex-shrink: 0;
}

.userDetails {
  @include flex-column;
  gap: $spacing-0-5;
  min-width: 0;

  .collapsed & {
    display: none;
  }
}

.userName {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-900;
  @include truncate;
}

.userRole {
  font-size: $font-size-xs;
  color: $gray-500;
  @include truncate;
}

// Responsive adjustments
@include responsive(lg) {
  .sidebar {
    position: fixed;
    transform: translateX(0);
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .sidebar {
    transition: none;
  }

  .navigationLink {
    transition: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .sidebar {
    border-right: 2px solid $gray-900;
  }

  .navigationLink {
    &.active {
      border: 2px solid $primary-600;
    }
  }
}