// Authentication hooks using TanStack Query and Zustand
// Provides reactive authentication state and actions

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useRouter } from '@tanstack/react-router'
import { useAuthStore } from '../stores/auth-store'
import { useNotifications } from '../stores/ui-store'
import { AuthService } from '../services/auth-service'
import { queryKeys } from '../services/query-keys'
import type { LoginCredentials, StaffUser } from '../types/api-types'

/**
 * Main authentication hook
 */
export const useAuth = () => {
  const {
    user,
    permissions,
    groups,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    getCurrentUser,
    initializeAuth,
    checkPermission,
    hasGroup,
    clearError,
    updateProfile,
    changePassword,
    isAdmin,
    isSuperUser,
    canManageOrders,
    canManageProducts,
    canManageCustomers,
    canManageStaff,
    canViewAnalytics,
  } = useAuthStore()

  return {
    // State
    user,
    permissions,
    groups,
    isAuthenticated,
    isLoading,
    error,

    // Actions
    login,
    logout,
    getCurrentUser,
    initializeAuth,
    checkPermission,
    hasGroup,
    clearError,
    updateProfile,
    changePassword,

    // Computed
    isAdmin: isAdmin(),
    isSuperUser: isSuperUser(),
    canManageOrders: canManageOrders(),
    canManageProducts: canManageProducts(),
    canManageCustomers: canManageCustomers(),
    canManageStaff: canManageStaff(),
    canViewAnalytics: canViewAnalytics(),
  }
}

/**
 * Login mutation hook
 */
export const useLogin = (options?: {
  onSuccess?: () => void
  onError?: (error: Error) => void
}) => {
  const { login } = useAuthStore()
  const { showSuccess, showError } = useNotifications()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (credentials: LoginCredentials) => login(credentials),
    onSuccess: () => {
      showSuccess('Welcome back!', 'You have been successfully logged in.')
      // Invalidate auth-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.all })
      // Call custom onSuccess if provided
      options?.onSuccess?.()
    },
    onError: (error: Error) => {
      showError('Login Failed', error.message)
      // Call custom onError if provided
      options?.onError?.(error)
    },
  })
}

/**
 * Logout mutation hook
 */
export const useLogout = () => {
  const { logout } = useAuthStore()
  const { showSuccess } = useNotifications()
  const queryClient = useQueryClient()
  const router = useRouter()

  return useMutation({
    mutationFn: () => logout(),
    onSuccess: () => {
      showSuccess('Logged out', 'You have been successfully logged out.')
      // Clear all cached data on logout
      queryClient.clear()
      // Navigate to login page after successful logout
      router.navigate({ to: '/login' })
    },
    onError: (error: Error) => {
      console.error('Logout error:', error)
      // Even if logout fails on server, clear local state
      queryClient.clear()
      // Navigate to login page even if logout fails
      router.navigate({ to: '/login' })
    },
  })
}

/**
 * Current user query hook
 */
export const useCurrentUser = () => {
  const { isAuthenticated } = useAuthStore()

  return useQuery({
    queryKey: queryKeys.auth.user(),
    queryFn: AuthService.getCurrentUser,
    enabled: isAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: false, // Don't retry auth queries
  })
}

/**
 * User permissions query hook
 */
export const useUserPermissions = () => {
  const { isAuthenticated } = useAuthStore()

  return useQuery({
    queryKey: queryKeys.auth.permissions(),
    queryFn: AuthService.getUserPermissions,
    enabled: isAuthenticated,
    staleTime: 15 * 60 * 1000, // 15 minutes - permissions don't change often
    retry: false,
  })
}

/**
 * Permission check hook
 */
export const usePermissionCheck = (permission: string) => {
  const { checkPermission, isAuthenticated } = useAuthStore()

  return useQuery({
    queryKey: queryKeys.auth.checkPermission(permission),
    queryFn: () => AuthService.checkPermission(permission),
    enabled: isAuthenticated && !checkPermission(permission), // Only check server if not already granted locally
    staleTime: 10 * 60 * 1000,
    retry: false,
    // Return local check result immediately if available
    placeholderData: checkPermission(permission),
  })
}

/**
 * Update profile mutation hook
 */
export const useUpdateProfile = () => {
  const { updateProfile } = useAuthStore()
  const { showSuccess, showError } = useNotifications()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: Partial<StaffUser>) => updateProfile(data),
    onSuccess: (updatedUser) => {
      showSuccess('Profile Updated', 'Your profile has been successfully updated.')
      // Update cached user data
      queryClient.setQueryData(queryKeys.auth.user(), (oldData: { user: StaffUser } | undefined) => ({
        ...oldData,
        user: updatedUser,
      }))
    },
    onError: (error: Error) => {
      showError('Update Failed', error.message)
    },
  })
}

/**
 * Change password mutation hook
 */
export const useChangePassword = () => {
  const { changePassword } = useAuthStore()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: { current_password: string; new_password: string; confirm_password: string }) =>
      changePassword(data),
    onSuccess: () => {
      showSuccess('Password Changed', 'Your password has been successfully updated.')
    },
    onError: (error: Error) => {
      showError('Password Change Failed', error.message)
    },
  })
}

/**
 * Password reset request mutation hook
 */
export const useRequestPasswordReset = () => {
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (email: string) => AuthService.requestPasswordReset(email),
    onSuccess: () => {
      showSuccess(
        'Reset Email Sent',
        'If an account with that email exists, you will receive password reset instructions.'
      )
    },
    onError: (error: Error) => {
      showError('Reset Request Failed', error.message)
    },
  })
}

/**
 * Password reset mutation hook
 */
export const useResetPassword = () => {
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: { token: string; new_password: string; confirm_password: string }) =>
      AuthService.confirmPasswordReset(data),
    onSuccess: () => {
      showSuccess('Password Reset', 'Your password has been successfully reset. You can now log in.')
    },
    onError: (error: Error) => {
      showError('Password Reset Failed', error.message)
    },
  })
}

/**
 * Token verification hook
 */
export const useVerifyResetToken = (token: string) => {
  return useQuery({
    queryKey: ['auth', 'verify-reset-token', token],
    queryFn: () => AuthService.verifyPasswordResetToken(token),
    enabled: !!token,
    retry: false,
    staleTime: 0, // Always fresh
  })
}

/**
 * Authentication guard hook for components
 */
export const useAuthGuard = (options: {
  permission?: string
  group?: string
  redirectTo?: string
} = {}) => {
  const { permission, group, redirectTo = '/login' } = options
  const { isAuthenticated, checkPermission, hasGroup, user } = useAuthStore()

  // Check authentication
  if (!isAuthenticated) {
    return {
      isAuthenticated: false,
      hasPermission: false,
      hasGroup: false,
      user: null,
      shouldRedirect: true,
      redirectTo,
    }
  }

  // Superuser bypasses all checks
  if (user?.is_superuser) {
    return {
      isAuthenticated: true,
      hasPermission: true,
      hasGroup: true,
      user,
      shouldRedirect: false,
      redirectTo: null,
    }
  }

  // Check specific permission
  const hasRequiredPermission = permission ? checkPermission(permission) : true

  // Check group membership
  const hasRequiredGroup = group ? hasGroup(group) : true

  const shouldRedirect = !hasRequiredPermission || !hasRequiredGroup

  return {
    isAuthenticated: true,
    hasPermission: hasRequiredPermission,
    hasGroup: hasRequiredGroup,
    user,
    shouldRedirect,
    redirectTo: shouldRedirect ? '/unauthorized' : null,
  }
}
