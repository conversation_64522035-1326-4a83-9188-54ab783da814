import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { ProductService } from '../../services/product-service'
import { useNotifications } from '../../stores/ui-store'
import { queryKeys } from '../../services/query-keys'
import type { AttributeValue, AttributeValueFormData, AttributeValueCreateData, AttributeValueBulkCreateData } from '../../types/api-types'

/**
 * Hook for fetching attribute values
 */
export const useAttributeValues = (attributeId?: number) => {
  return useQuery({
    queryKey: queryKeys.products.attributeValues(attributeId),
    queryFn: () => ProductService.getAttributeValues(attributeId),
    enabled: !!attributeId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for fetching a single attribute value
 */
export const useAttributeValue = (id: number) => {
  return useQuery({
    queryKey: queryKeys.products.attributeValue(id),
    queryFn: () => ProductService.getAttributeValue(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for creating an attribute value
 */
export const useCreateAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: AttributeValueCreateData) => ProductService.createAttributeValue(data),
    onSuccess: (newValue) => {
      showSuccess('Value Created', 'Attribute value has been created successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.attributes.values(newValue.attribute) })
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create attribute value.')
    },
  })
}

/**
 * Hook for updating an attribute value
 */
export const useUpdateAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: AttributeValueFormData }) =>
      ProductService.updateAttributeValue(id, data),
    onSuccess: (updatedValue) => {
      showSuccess('Value Updated', 'Attribute value has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributeValue(updatedValue.id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributeValues(updatedValue.attribute) })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute value.')
    },
  })
}

/**
 * Hook for deleting an attribute value
 */
export const useDeleteAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, attributeId }: { id: number; attributeId: number }) =>
      ProductService.deleteAttributeValue(id),
    onSuccess: (_, variables) => {
      showSuccess('Value Deleted', 'Attribute value has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributeValues(variables.attributeId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributeValue(variables.id) })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete attribute value.')
    },
  })
}

/**
 * Hook for bulk creating attribute values
 */
export const useBulkCreateAttributeValues = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: AttributeValueBulkCreateData) => ProductService.bulkCreateAttributeValues(data),
    onSuccess: (_, variables) => {
      showSuccess('Values Created', 'Attribute values have been created successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributeValues(variables.attribute_id) })
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create attribute values.')
    },
  })
}
