import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { ProductService } from '../../services/product-service'
import { useNotifications } from '../../stores/ui-store'
import { queryKeys } from '../../services/query-keys'
import type { BrandProductType, BrandProductTypeBulkAssociate } from '../../types/api-types'

/**
 * Hook for fetching product types associated with a brand
 */
export const useBrandProductTypes = (brandId: number) => {
  return useQuery({
    queryKey: queryKeys.brands.productTypes(brandId),
    queryFn: () => ProductService.getBrandProductTypes(brandId),
    enabled: !!brandId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for fetching all brand-product type associations
 */
export const useBrandProductTypeAssociations = () => {
  return useQuery({
    queryKey: queryKeys.brands.allProductTypeAssociations(),
    queryFn: ProductService.getAllBrandProductTypeAssociations,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for bulk associating brands with product types
 */
export const useBulkAssociateBrandProductTypes = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: BrandProductTypeBulkAssociate) =>
      ProductService.bulkAssociateBrandProductTypes(data),
    onSuccess: () => {
      showSuccess('Associations Updated', 'Brand-product type associations have been updated successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.brands.allProductTypeAssociations() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update brand-product type associations.')
    },
  })
}

/**
 * Hook for deleting a brand-product type association
 */
export const useDeleteBrandProductTypeAssociation = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (id: number) => ProductService.deleteBrandProductTypeAssociation(id),
    onSuccess: () => {
      showSuccess('Association Deleted', 'Brand-product type association has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.brands.allProductTypeAssociations() })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete brand-product type association.')
    },
  })
}


// Hooks for associating/removing multiple brands from a product type

/**
 * Hook for fetching brands associated with a product type
 */
export const useProductTypeBrands = (productTypeId: number) => {
  return useQuery({
    queryKey: queryKeys.products.productTypeBrands(productTypeId),
    queryFn: () => ProductService.getProductTypeBrands(productTypeId),
    enabled: !!productTypeId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for associating multiple brands with a product type
 */
export const useAssociateBrandsWithProductType = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productTypeId, brandIds }: { productTypeId: number; brandIds: number[] }) =>
      ProductService.associateBrandsWithProductType(productTypeId, brandIds),
    onSuccess: (_, variables) => {
      showSuccess('Brands Associated', 'Brands have been associated with the product type successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeBrands(variables.productTypeId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brandProductTypes() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
    },
    onError: (error: any) => {
      showError('Association Failed', error.message || 'Failed to associate brands with product type.')
    },
  })
}

/**
 * Hook for removing multiple brands from a product type
 */
export const useRemoveBrandsFromProductType = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productTypeId, brandIds }: { productTypeId: number; brandIds: number[] }) =>
      ProductService.removeBrandsFromProductType(productTypeId, brandIds),
    onSuccess: (_, variables) => {
      showSuccess('Brands Removed', 'Brands have been removed from the product type successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeBrands(variables.productTypeId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brandProductTypes() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
    },
    onError: (error: any) => {
      showError('Removal Failed', error.message || 'Failed to remove brands from product type.')
    },
  })
}

