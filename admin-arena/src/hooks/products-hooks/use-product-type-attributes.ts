import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { ProductService } from '../../services/product-service'
import { useNotifications } from '../../stores/ui-store'
import { queryKeys } from '../../services/query-keys'
import type { ProductTypeAttribute, ProductTypeAttributeAssociation } from '../../types/api-types'

/**
 * Hook for fetching attributes associated with a product type
 */
export const useProductTypeAttributes = (productTypeId?: number) => {
  return useQuery({
    queryKey: queryKeys.products.productTypeAttributes(productTypeId!),
    queryFn: () => ProductService.getProductTypeAttributes(productTypeId!),
    enabled: !!productTypeId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for associating attributes with a product type
 */
export const useAssociateProductTypeAttributes = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: { productTypeId: number; attributes: ProductTypeAttributeAssociation[] }) =>
      ProductService.associateProductTypeAttributes(data.productTypeId, data.attributes),
    onSuccess: (_, { productTypeId }) => {
      showSuccess('Attributes Associated', 'Attributes have been associated with the product type successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeAttributes(productTypeId) })
    },
    onError: (error: any) => {
      showError('Association Failed', error.message || 'Failed to associate attributes with product type.')
    },
  })
}

/**
 * Hook for updating product type attribute association
 */
export const useUpdateProductTypeAttribute = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({
      productTypeId,
      attributeId,
      data,
    }: {
      productTypeId: number
      attributeId: number
      data: Partial<ProductTypeAttribute>
    }) => ProductService.updateProductTypeAttribute(productTypeId, attributeId, data),
    onSuccess: (updatedAssociation, { productTypeId }) => {
      showSuccess('Attribute Updated', 'Product type attribute has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeAttributes(productTypeId) })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product type attribute.')
    },
  })
}

/**
 * Hook for removing an attribute from a product type
 */
export const useRemoveProductTypeAttribute = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: { productTypeId: number; attributeId: number }) =>
      ProductService.removeProductTypeAttribute(data.productTypeId, data.attributeId),
    onSuccess: (_, { productTypeId }) => {
      showSuccess('Attribute Removed', 'Attribute has been removed from the product type successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeAttributes(productTypeId) })
    },
    onError: (error: any) => {
      showError('Removal Failed', error.message || 'Failed to remove attribute from product type.')
    },
  })
}

/**
 * Hook for updating product type attribute association using association ID
 */
export const useUpdateProductTypeAttributeAssociation = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ associationId, data, productTypeId }: {
      associationId: number
      data: Partial<ProductTypeAttributeAssociation>
      productTypeId: number
    }) => ProductService.updateProductTypeAttributeAssociation(associationId, data),
    onSuccess: (_, variables) => {
      showSuccess('Association Updated', 'Product type attribute association has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeAttributes(variables.productTypeId) })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product type attribute association.')
    },
  })
}

/**
 * Hook for deleting product type attribute association using association ID
 */
export const useDeleteProductTypeAttributeAssociation = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ associationId, productTypeId }: { associationId: number; productTypeId: number }) =>
      ProductService.deleteProductTypeAttributeAssociation(associationId),
    onSuccess: (_, variables) => {
      showSuccess('Association Deleted', 'Product type attribute association has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeAttributes(variables.productTypeId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete product type attribute association.')
    },
  })
}
