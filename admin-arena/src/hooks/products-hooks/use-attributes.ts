import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ProductService } from '../../services/product-service';
import { useNotifications } from '../../stores/ui-store';
import { queryKeys } from '../../services/query-keys';
import type { Attribute, AttributeValue } from '../../types/api-types';

/**
 * Hook for fetching product attributes
 */
export const useAttributes = () => {
  return useQuery({
    queryKey: queryKeys.products.attributes(),
    queryFn: ProductService.getAttributes,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
};

/**
 * Hook for creating a new attribute
 */
export const useCreateAttribute = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ProductService.createAttribute,
    onSuccess: (newAttribute) => {
      showSuccess('Attribute Created', `${newAttribute.name} has been created successfully.`);
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributes() });
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create attribute.');
    },
  });
};

/**
 * Hook for updating an attribute
 */
export const useUpdateAttribute = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<Attribute> }) =>
      ProductService.updateAttribute(id, data),
    onSuccess: (updatedAttribute) => {
      showSuccess('Attribute Updated', `${updatedAttribute.name} has been updated successfully.`);
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributes() });
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attribute(updatedAttribute.id) });
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute.');
    },
  });
};

/**
 * Hook for deleting an attribute
 */
export const useDeleteAttribute = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: (id: number) => ProductService.deleteAttribute(id),
    onSuccess: (_, variables) => {
      showSuccess('Attribute Deleted', 'Attribute has been deleted successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributes() });
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attribute(variables) });
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete attribute.');
    },
  });
};
