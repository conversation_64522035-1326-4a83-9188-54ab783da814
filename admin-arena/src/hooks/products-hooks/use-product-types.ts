import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ProductService } from '../../services/product-service';
import { useNotifications } from '../../stores/ui-store';
import { queryKeys } from '../../services/query-keys';
import type { ProductType } from '../../types/api-types';

/**
 * Hook for fetching product types
 */
export const useProductTypes = () => {
  return useQuery({
    queryKey: queryKeys.products.types(),
    queryFn: ProductService.getProductTypes,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
};

/**
 * Hook for creating a new product type
 */
export const useCreateProductType = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ProductService.createProductType,
    onSuccess: (newType) => {
      showSuccess('Product Type Created', `${newType.name} has been created successfully.`);
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() });
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create product type.');
    },
  });
};

/**
 * Hook for updating a product type
 */
export const useUpdateProductType = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: { title: string } }) =>
      ProductService.updateProductType(id, data),
    onSuccess: (updatedType) => {
      showSuccess('Product Type Updated', `${updatedType.title} has been updated successfully.`);
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() });
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product type.');
    },
  });
};

/**
 * Hook for deleting a product type
 */
export const useDeleteProductType = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: (id: number) => ProductService.deleteProductType(id),
    onSuccess: (_, productTypeId) => {
      showSuccess('Product Type Deleted', 'Product type has been deleted successfully.');
      
      // Invalidate product types list
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() });
      
      // Invalidate any related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete product type.');
    },
  });
};
