// UI state management store for global UI components
// Handles sidebar, notifications, modals, and theme

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Notification } from '../types/api-types';

interface Modal {
  id: string;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closable?: boolean;
}

interface UIState {
  // Sidebar
  sidebarCollapsed: boolean;
  sidebarMobileOpen: boolean;
  
  // Notifications
  notifications: Notification[];
  
  // Modals
  modals: Modal[];
  
  // Loading states
  globalLoading: boolean;
  
  // Theme
  theme: 'light' | 'dark';
  
  // Page state
  pageTitle: string;
  breadcrumbs: Array<{ label: string; path?: string }>;
  
  // Actions
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  toggleMobileSidebar: () => void;
  
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  openModal: (modal: Omit<Modal, 'id'>) => string;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
  
  setGlobalLoading: (loading: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  
  setPageTitle: (title: string) => void;
  setBreadcrumbs: (breadcrumbs: Array<{ label: string; path?: string }>) => void;
}

export const useUIStore = create<UIState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        sidebarCollapsed: false,
        sidebarMobileOpen: false,
        notifications: [],
        modals: [],
        globalLoading: false,
        theme: 'light',
        pageTitle: 'Admin Arena',
        breadcrumbs: [],
        
        // Sidebar actions
        toggleSidebar: () => set((state) => ({ 
          sidebarCollapsed: !state.sidebarCollapsed 
        })),
        
        setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
        
        toggleMobileSidebar: () => set((state) => ({ 
          sidebarMobileOpen: !state.sidebarMobileOpen 
        })),
        
        // Notification actions
        addNotification: (notification) => {
          const id = Math.random().toString(36).substr(2, 9);
          const newNotification = { ...notification, id };
          
          set((state) => ({
            notifications: [...state.notifications, newNotification]
          }));
          
          // Auto-remove notification after duration (unless duration is 0)
          if (notification.duration !== 0) {
            setTimeout(() => {
              get().removeNotification(id);
            }, notification.duration || 5000);
          }
        },
        
        removeNotification: (id) => set((state) => ({
          notifications: state.notifications.filter(n => n.id !== id)
        })),
        
        clearNotifications: () => set({ notifications: [] }),
        
        // Modal actions
        openModal: (modal) => {
          const id = Math.random().toString(36).substr(2, 9);
          const newModal = { ...modal, id };
          
          set((state) => ({
            modals: [...state.modals, newModal]
          }));
          
          return id;
        },
        
        closeModal: (id) => set((state) => ({
          modals: state.modals.filter(m => m.id !== id)
        })),
        
        closeAllModals: () => set({ modals: [] }),
        
        // Global actions
        setGlobalLoading: (loading) => set({ globalLoading: loading }),
        
        setTheme: (theme) => set({ theme }),
        
        // Page state actions
        setPageTitle: (title) => set({ pageTitle: title }),
        
        setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs }),
      }),
      {
        name: 'admin-ui-store',
        partialize: (state) => ({
          sidebarCollapsed: state.sidebarCollapsed,
          theme: state.theme,
          // Don't persist notifications, modals, or loading states
        }),
        version: 1,
      }
    ),
    { 
      name: 'UIStore',
      enabled: import.meta.env.DEV
    }
  )
);

// Notification helper hooks
export const useNotifications = () => {
  const { addNotification, removeNotification, clearNotifications, notifications } = useUIStore();
  
  const showSuccess = (title: string, message?: string, duration?: number) => {
    addNotification({ type: 'success', title, message, duration });
  };
  
  const showError = (title: string, message?: string) => {
    addNotification({ type: 'error', title, message, duration: 0 }); // Errors don't auto-dismiss
  };
  
  const showWarning = (title: string, message?: string, duration?: number) => {
    addNotification({ type: 'warning', title, message, duration });
  };
  
  const showInfo = (title: string, message?: string, duration?: number) => {
    addNotification({ type: 'info', title, message, duration });
  };
  
  return {
    notifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    removeNotification,
    clearNotifications
  };
};

// Modal helper hooks
export const useModals = () => {
  const { modals, openModal, closeModal, closeAllModals } = useUIStore();
  
  return {
    modals,
    openModal,
    closeModal,
    closeAllModals
  };
};

// Sidebar helper hooks
export const useSidebar = () => {
  const { 
    sidebarCollapsed, 
    sidebarMobileOpen, 
    toggleSidebar, 
    setSidebarCollapsed, 
    toggleMobileSidebar 
  } = useUIStore();
  
  return {
    sidebarCollapsed,
    sidebarMobileOpen,
    toggleSidebar,
    setSidebarCollapsed,
    toggleMobileSidebar
  };
};

// Theme helper hooks
export const useTheme = () => {
  const { theme, setTheme } = useUIStore();
  
  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };
  
  return {
    theme,
    setTheme,
    toggleTheme
  };
};

// Page state helper hooks
export const usePageState = () => {
  const { pageTitle, breadcrumbs, setPageTitle, setBreadcrumbs } = useUIStore();
  
  return {
    pageTitle,
    breadcrumbs,
    setPageTitle,
    setBreadcrumbs
  };
};

// Selector hooks for better performance
export const useGlobalLoading = () => useUIStore((state) => state.globalLoading);
export const useNotificationCount = () => useUIStore((state) => state.notifications.length);
export const useModalCount = () => useUIStore((state) => state.modals.length);
