// Pure cookie-based authentication store
// No persistence needed - authentication state is managed server-side via HTTP-only cookies

import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { AuthService } from '../services/auth-service'
import { StaffUser, LoginCredentials } from '../types/api-types'

interface AuthState {
  // State
  user: StaffUser | null
  permissions: string[]
  groups: string[]
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => Promise<void>
  getCurrentUser: () => Promise<void>
  initializeAuth: () => Promise<boolean>
  checkPermission: (permission: string) => boolean
  hasGroup: (group: string) => boolean
  clearError: () => void
  setUser: (user: StaffUser) => void
  setPermissions: (permissions: string[]) => void
  setGroups: (groups: string[]) => void
  updateProfile: (data: Partial<StaffUser>) => Promise<void>
  changePassword: (data: { current_password: string; new_password: string; confirm_password: string }) => Promise<void>

  // Computed getters
  isAdmin: () => boolean
  isSuperUser: () => boolean
  canManageOrders: () => boolean
  canManageProducts: () => boolean
  canManageCustomers: () => boolean
  canManageStaff: () => boolean
  canViewAnalytics: () => boolean
}

export const useAuthStore = create<AuthState>()(
  devtools(
    (set, get) => ({
      // Initial state
      user: null,
      permissions: [],
      groups: [],
      isAuthenticated: false,
      isLoading: true, // Start with loading true to prevent premature redirects
      error: null,

      // Authentication actions
      login: async (credentials) => {
        set({ isLoading: true, error: null })
        try {
          const response = await AuthService.login(credentials)
          const userResponse = await AuthService.getCurrentUser()

          set({
            user: response.user,
            permissions: userResponse.permissions,
            groups: userResponse.groups,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Login failed'
          set({
            error: errorMessage,
            isLoading: false,
            isAuthenticated: false,
            user: null,
            permissions: [],
            groups: []
          })
          throw error
        }
      },

      logout: async () => {
        set({ isLoading: true })
        try {
          await AuthService.logout()
        } catch (error) {
          console.warn('Logout error:', error)
        } finally {
          set({
            user: null,
            permissions: [],
            groups: [],
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
        }
      },

      getCurrentUser: async () => {
        set({ isLoading: true, error: null })
        try {
          const response = await AuthService.getCurrentUser()
          set({
            user: response.user,
            permissions: response.permissions,
            groups: response.groups,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
        } catch (error) {
          console.error('Failed to get current user:', error)
          set({
            user: null,
            permissions: [],
            groups: [],
            isAuthenticated: false,
            isLoading: false,
            error: 'Failed to authenticate user'
          })
          throw error
        }
      },

      // Initialize authentication state on app load
      // This method is specifically for checking authentication on page refresh
      initializeAuth: async () => {
        set({ isLoading: true, error: null })
        try {
          const response = await AuthService.getCurrentUser()
          set({
            user: response.user,
            permissions: response.permissions,
            groups: response.groups,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
          return true
        } catch (error) {
          // If getCurrentUser fails, user is not authenticated
          // This is expected for unauthenticated users, so don't set error
          set({
            isLoading: false,
            isAuthenticated: false,
            user: null,
            permissions: [],
            groups: [],
            error: null
          })
          return false
        }
      },

      // Permission checking
      checkPermission: (permission: string) => {
        const { permissions, user } = get()
        return user?.is_superuser || permissions.includes(permission)
      },

      hasGroup: (group: string) => {
        const { groups, user } = get()
        return user?.is_superuser || groups.includes(group)
      },

      // State management
      clearError: () => set({ error: null }),

      setUser: (user: StaffUser) => set({ user }),

      setPermissions: (permissions: string[]) => set({ permissions }),

      setGroups: (groups: string[]) => set({ groups }),

      // Profile management
      updateProfile: async (data: Partial<StaffUser>) => {
        set({ isLoading: true, error: null })
        try {
          const updatedUser = await AuthService.updateProfile(data)
          set({
            user: updatedUser,
            isLoading: false
          })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update profile'
          set({
            error: errorMessage,
            isLoading: false
          })
          throw error
        }
      },

      changePassword: async (data) => {
        set({ isLoading: true, error: null })
        try {
          await AuthService.changePassword(data)
          set({ isLoading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to change password'
          set({
            error: errorMessage,
            isLoading: false
          })
          throw error
        }
      },

      // Computed getters
      isAdmin: () => {
        const { user } = get()
        return user?.is_staff || false
      },

      isSuperUser: () => {
        const { user } = get()
        return user?.is_superuser || false
      },

      canManageOrders: () => {
        const { checkPermission, hasGroup } = get()
        return checkPermission('staff.view_orderproxy') ||
          hasGroup('Order Management Executive (OME)') ||
          hasGroup('Order Management Group Member (OMGM)')
      },

      canManageProducts: () => {
        const { checkPermission, hasGroup } = get()
        return checkPermission('staff.view_productproxy') ||
          hasGroup('Product Management Executive (PME)') ||
          hasGroup('Product Management Group Member (PMGM)')
      },

      canManageCustomers: () => {
        const { checkPermission, hasGroup } = get()
        return checkPermission('staff.view_customerproxy') ||
          hasGroup('Customer Management Executive (CME)') ||
          hasGroup('Customer Service Representative (CSR)')
      },

      canManageStaff: () => {
        const { checkPermission, hasGroup } = get()
        return checkPermission('staff.view_staffprofile') ||
          hasGroup('Staff Manager (SM)') ||
          hasGroup('HR Administrator (HRA)')
      },

      canViewAnalytics: () => {
        const { checkPermission, hasGroup } = get()
        return checkPermission('staff.view_analytics') ||
          hasGroup('Business Intelligence Analyst (BIA)') ||
          hasGroup('Finance Manager (FM)')
      },
    }),
    {
      name: 'AuthStore',
      // Enable Redux DevTools integration
      enabled: import.meta.env.DEV
    }
  )
)

// Selector hooks for better performance
export const useAuthUser = () => useAuthStore((state) => state.user)
export const useAuthPermissions = () => useAuthStore((state) => state.permissions)
export const useAuthGroups = () => useAuthStore((state) => state.groups)
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated)
export const useAuthLoading = () => useAuthStore((state) => state.isLoading)
export const useAuthError = () => useAuthStore((state) => state.error)

// Permission checking hooks
export const useCanManageOrders = () => useAuthStore((state) => state.canManageOrders())
export const useCanManageProducts = () => useAuthStore((state) => state.canManageProducts())
export const useCanManageCustomers = () => useAuthStore((state) => state.canManageCustomers())
export const useCanManageStaff = () => useAuthStore((state) => state.canManageStaff())
export const useCanViewAnalytics = () => useAuthStore((state) => state.canViewAnalytics())
export const useIsSuperUser = () => useAuthStore((state) => state.isSuperUser())
