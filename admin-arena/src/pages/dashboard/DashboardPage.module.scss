// Dashboard page styles
// Modern admin dashboard with cards and responsive grid

@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.dashboard {
  padding: $spacing-6;
  max-width: 1400px;
  margin: 0 auto;

  @include responsive(sm) {
    padding: $spacing-8;
  }
}

.header {
  @include flex-between;
  margin-bottom: $spacing-8;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.title {
  font-size: $font-size-3xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
  margin: 0 0 $spacing-2 0;
  line-height: $line-height-tight;

  @include responsive(sm) {
    font-size: $font-size-4xl;
  }
}

.subtitle {
  font-size: $font-size-lg;
  color: $gray-600;
  margin: 0;
  line-height: $line-height-normal;
}

.headerActions {
  display: flex;
  gap: $spacing-3;

  @include mobile-only {
    width: 100%;

    button {
      flex: 1;
    }
  }
}

// Stats Grid
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: $spacing-6;
  margin-bottom: $spacing-8;
}

.statCard {
  @include card;
  padding: $spacing-6;
  transition: $transition-shadow;

  &:hover {
    box-shadow: $shadow-md;
  }
}

.statHeader {
  @include flex-between;
  margin-bottom: $spacing-4;
}

.statIcon {
  @include flex-center;
  width: $spacing-12;
  height: $spacing-12;
  background: linear-gradient(135deg, $primary-500, $primary-600);
  border-radius: $border-radius-lg;
  color: white;

  svg {
    width: $spacing-6;
    height: $spacing-6;
  }
}

.statChange {
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  color: $success-600;
  background-color: $success-50;
  padding: $spacing-1 $spacing-2;
  border-radius: $border-radius;
}

.statContent {
  .statValue {
    font-size: $font-size-3xl;
    font-weight: $font-weight-bold;
    color: $gray-900;
    margin: 0 0 $spacing-1 0;
    line-height: $line-height-tight;
  }

  .statLabel {
    font-size: $font-size-base;
    color: $gray-600;
    margin: 0 0 $spacing-3 0;
    line-height: $line-height-normal;
  }
}

.statDetails {
  display: flex;
  gap: $spacing-4;
  font-size: $font-size-sm;
  color: $gray-500;

  span {
    &:not(:last-child)::after {
      content: '•';
      margin-left: $spacing-2;
      color: $gray-300;
    }
  }
}

// Content Grid
.contentGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-8;

  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.quickActions,
.recentActivity {
  @include card;
  padding: $spacing-6;
}

.sectionTitle {
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
  color: $gray-900;
  margin: 0 0 $spacing-6 0;
  line-height: $line-height-tight;
}

// Quick Actions
.actionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-4;
}

// Recent Activity
.activityList {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.activityItem {
  display: flex;
  gap: $spacing-3;
  padding: $spacing-3;
  border-radius: $border-radius;
  transition: $transition-colors;

  &:hover {
    background-color: $gray-50;
  }
}

.activityIcon {
  @include flex-center;
  width: $spacing-8;
  height: $spacing-8;
  background-color: $gray-100;
  border-radius: $border-radius;
  color: $gray-600;
  flex-shrink: 0;

  svg {
    width: $spacing-4;
    height: $spacing-4;
  }
}

.activityContent {
  flex: 1;
  min-width: 0;
}

.activityMessage {
  font-size: $font-size-sm;
  color: $gray-900;
  margin: 0 0 $spacing-1 0;
  line-height: $line-height-normal;

  @include truncate;
}

.activityTime {
  @include flex-start;
  gap: $spacing-1;
  font-size: $font-size-xs;
  color: $gray-500;

  svg {
    width: $spacing-3;
    height: $spacing-3;
  }
}

// Responsive adjustments
@include responsive(lg) {
  .statsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@include responsive(xl) {
  .dashboard {
    padding: $spacing-10;
  }

  .statsGrid {
    gap: $spacing-8;
  }

  .contentGrid {
    gap: $spacing-10;
  }
}

// Loading states
.statCard {
  &.loading {
    .statValue {
      background: linear-gradient(90deg, $gray-200 25%, $gray-100 50%, $gray-200 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: $border-radius;
      color: transparent;
    }
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

// Dark mode support (future enhancement)
@media (prefers-color-scheme: dark) {
  .title {
    color: $gray-100;
  }

  .subtitle {
    color: $gray-300;
  }

  .statCard {
    background-color: $gray-800;
    border-color: $gray-700;
  }
}