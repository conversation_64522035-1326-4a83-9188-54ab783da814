// Main dashboard page with key metrics and overview
// Provides quick access to important admin functions

import React, { useEffect } from 'react';
import { 
  FiShoppingCart, 
  FiPackage, 
  FiUsers, 
  FiDollarSign,
  FiTrendingUp,
  FiActivity,
  FiClock,
  FiAlertCircle
} from 'react-icons/fi';
import { useAuth } from '../../hooks/use-auth';
import { usePageState } from '../../stores/ui-store';
import { Button } from '../../components/ui/Button';
import styles from './DashboardPage.module.scss';

// Mock data - will be replaced with real API calls
const mockStats = {
  orders: {
    total: 1247,
    pending: 23,
    processing: 45,
    shipped: 67,
    delivered: 1089,
    cancelled: 23,
    change: '+12%'
  },
  products: {
    total: 856,
    active: 823,
    inactive: 33,
    low_stock: 12,
    change: '+3%'
  },
  customers: {
    total: 2341,
    active: 2198,
    new_this_month: 143,
    change: '+8%'
  },
  revenue: {
    today: 12450.50,
    this_week: 87320.25,
    this_month: 342150.75,
    this_year: 2847650.00,
    change: '+15%'
  }
};

const mockRecentActivity = [
  { id: 1, type: 'order', message: 'New order #ORD-001234 received', time: '2 minutes ago' },
  { id: 2, type: 'product', message: 'Product "Wireless Headphones" updated', time: '15 minutes ago' },
  { id: 3, type: 'customer', message: 'New customer registration: <EMAIL>', time: '1 hour ago' },
  { id: 4, type: 'order', message: 'Order #ORD-001230 shipped', time: '2 hours ago' },
  { id: 5, type: 'alert', message: 'Low stock alert: 3 products below threshold', time: '3 hours ago' },
];

export const DashboardPage: React.FC = () => {
  const { user, canManageOrders, canManageProducts, canManageCustomers, canViewAnalytics } = useAuth();
  const { setPageTitle, setBreadcrumbs } = usePageState();

  useEffect(() => {
    setPageTitle('Dashboard');
    setBreadcrumbs([{ label: 'Dashboard' }]);
  }, [setPageTitle, setBreadcrumbs]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order': return <FiShoppingCart />;
      case 'product': return <FiPackage />;
      case 'customer': return <FiUsers />;
      case 'alert': return <FiAlertCircle />;
      default: return <FiActivity />;
    }
  };

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <div>
          <h1 className={styles.title}>Welcome back, {user?.staff_profile?.full_name || user?.email}!</h1>
          <p className={styles.subtitle}>Here's what's happening with your store today.</p>
        </div>
        <div className={styles.headerActions}>
          <Button variant="outline" leftIcon={<FiTrendingUp />}>
            View Reports
          </Button>
          <Button variant="primary" leftIcon={<FiActivity />}>
            Quick Actions
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className={styles.statsGrid}>
        {canViewAnalytics && (
          <div className={styles.statCard}>
            <div className={styles.statHeader}>
              <div className={styles.statIcon}>
                <FiDollarSign />
              </div>
              <span className={styles.statChange}>{mockStats.revenue.change}</span>
            </div>
            <div className={styles.statContent}>
              <h3 className={styles.statValue}>{formatCurrency(mockStats.revenue.this_month)}</h3>
              <p className={styles.statLabel}>Revenue This Month</p>
            </div>
          </div>
        )}

        {canManageOrders && (
          <div className={styles.statCard}>
            <div className={styles.statHeader}>
              <div className={styles.statIcon}>
                <FiShoppingCart />
              </div>
              <span className={styles.statChange}>{mockStats.orders.change}</span>
            </div>
            <div className={styles.statContent}>
              <h3 className={styles.statValue}>{mockStats.orders.total.toLocaleString()}</h3>
              <p className={styles.statLabel}>Total Orders</p>
              <div className={styles.statDetails}>
                <span>Pending: {mockStats.orders.pending}</span>
                <span>Processing: {mockStats.orders.processing}</span>
              </div>
            </div>
          </div>
        )}

        {canManageProducts && (
          <div className={styles.statCard}>
            <div className={styles.statHeader}>
              <div className={styles.statIcon}>
                <FiPackage />
              </div>
              <span className={styles.statChange}>{mockStats.products.change}</span>
            </div>
            <div className={styles.statContent}>
              <h3 className={styles.statValue}>{mockStats.products.total.toLocaleString()}</h3>
              <p className={styles.statLabel}>Products</p>
              <div className={styles.statDetails}>
                <span>Active: {mockStats.products.active}</span>
                <span>Low Stock: {mockStats.products.low_stock}</span>
              </div>
            </div>
          </div>
        )}

        {canManageCustomers && (
          <div className={styles.statCard}>
            <div className={styles.statHeader}>
              <div className={styles.statIcon}>
                <FiUsers />
              </div>
              <span className={styles.statChange}>{mockStats.customers.change}</span>
            </div>
            <div className={styles.statContent}>
              <h3 className={styles.statValue}>{mockStats.customers.total.toLocaleString()}</h3>
              <p className={styles.statLabel}>Customers</p>
              <div className={styles.statDetails}>
                <span>Active: {mockStats.customers.active}</span>
                <span>New: {mockStats.customers.new_this_month}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Quick Actions and Recent Activity */}
      <div className={styles.contentGrid}>
        <div className={styles.quickActions}>
          <h2 className={styles.sectionTitle}>Quick Actions</h2>
          <div className={styles.actionGrid}>
            {canManageOrders && (
              <Button variant="outline" fullWidth leftIcon={<FiShoppingCart />}>
                View Orders
              </Button>
            )}
            {canManageProducts && (
              <Button variant="outline" fullWidth leftIcon={<FiPackage />}>
                Add Product
              </Button>
            )}
            {canManageCustomers && (
              <Button variant="outline" fullWidth leftIcon={<FiUsers />}>
                Customer Support
              </Button>
            )}
            {canViewAnalytics && (
              <Button variant="outline" fullWidth leftIcon={<FiTrendingUp />}>
                Analytics
              </Button>
            )}
          </div>
        </div>

        <div className={styles.recentActivity}>
          <h2 className={styles.sectionTitle}>Recent Activity</h2>
          <div className={styles.activityList}>
            {mockRecentActivity.map((activity) => (
              <div key={activity.id} className={styles.activityItem}>
                <div className={styles.activityIcon}>
                  {getActivityIcon(activity.type)}
                </div>
                <div className={styles.activityContent}>
                  <p className={styles.activityMessage}>{activity.message}</p>
                  <span className={styles.activityTime}>
                    <FiClock /> {activity.time}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
