import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useNavigate } from '@tanstack/react-router'
import { FiMail, FiLock, FiEye, FiEyeOff } from 'react-icons/fi'
import { useLogin } from '../../hooks/use-auth'
import { Input } from '../../components/ui/Input'
import { Button } from '../../components/ui/Button'
import styles from './LoginPage.module.scss'

// Login form validation schema
const loginSchema = z.object({
  username: z
    .email('Please enter a valid email address')
    .min(1, 'Username is required'),
  password: z
    .string()
    .min(1, 'Password is required')
  // .min(6, 'Password must be at least 6 characters'),
})

type LoginFormData = z.infer<typeof loginSchema>

export const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  // const { isAuthenticated } = useAuth()
  const [showPassword, setShowPassword] = React.useState(false)

  const loginMutation = useLogin({
    onSuccess: () => {
      // Navigate to dashboard after successful login
      navigate({ to: '/' })
    }
  })

  const { register, handleSubmit, formState: { errors, isSubmitting }, setError, } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  })

  const onSubmit = async (data: LoginFormData) => {
    try {
      await loginMutation.mutateAsync(data)
      // Navigation will be handled by the mutation's onSuccess callback
    } catch (error: unknown) {
      // Handle specific error cases
      if (error instanceof Error) {
        if (error.message.includes('username') || error.message.includes('email')) {
          setError('username', { message: error.message })
        } else if (error.message.includes('password')) {
          setError('password', { message: error.message })
        } else {
          setError('root', { message: error.message })
        }
      } else {
        setError('root', { message: 'An unexpected error occurred' })
      }
    }
  }

  // Redirect if already authenticated (on initial load)
  // React.useEffect(() => {
  //   if (isAuthenticated) {
  //     navigate({ to: '/' })
  //   }
  // }, []) // Empty dependency array - only run on mount

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  return (
    <div className={styles.loginContainer}>
      <div className={styles.loginCard}>
        <div className={styles.header}>
          <h1 className={styles.title}>Admin Arena</h1>
          <p className={styles.subtitle}>Sign in to your admin account</p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className={styles.form} noValidate>
          {errors.root && (
            <div className={styles.errorAlert} role="alert">
              {errors.root.message}
            </div>
          )}

          <div className={styles.formGroup}>
            <Input
              {...register('username')}
              type="email"
              label="Email Address"
              placeholder="Enter your email"
              leftIcon={<FiMail />}
              error={errors.username?.message}
              autoComplete="email"
              autoFocus
              data-testid="username-input"
            />
          </div>

          <div className={styles.formGroup}>
            <Input
              {...register('password')}
              type={showPassword ? 'text' : 'password'}
              label="Password"
              placeholder="Enter your password"
              leftIcon={<FiLock />}
              rightIcon={
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className={styles.passwordToggle}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  {showPassword ? <FiEyeOff /> : <FiEye />}
                </button>
              }
              error={errors.password?.message}
              autoComplete="current-password"
              data-testid="password-input"
            />
          </div>

          <Button
            type="submit"
            variant="primary"
            size="lg"
            fullWidth
            loading={isSubmitting || loginMutation.isPending}
            loadingText="Signing in..."
            disabled={isSubmitting || loginMutation.isPending}
            data-testid="login-button"
          >
            Sign In
          </Button>
        </form>

        <div className={styles.footer}>
          <p className={styles.footerText}>
            Forgot your password?{' '}
            <button
              type="button"
              className={styles.link}
              onClick={() => {
                // TODO: Implement password reset
                console.log('Password reset not implemented yet')
              }}
            >
              Reset it here
            </button>
          </p>
        </div>
      </div>

      <div className={styles.background}>
        <div className={styles.backgroundPattern}></div>
      </div>
    </div>
  )
}

export default LoginPage
