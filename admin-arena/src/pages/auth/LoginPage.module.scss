// Login page styles
// Modern, professional design for admin authentication

@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.loginContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, $primary-600 0%, $primary-800 100%);
  padding: $spacing-4;
}

.loginCard {
  @include card;
  width: 100%;
  max-width: 400px;
  padding: $spacing-8;
  position: relative;
  z-index: 1;
  box-shadow: $shadow-2xl;

  @include responsive(sm) {
    padding: $spacing-10;
  }
}

.header {
  text-align: center;
  margin-bottom: $spacing-8;
}

.title {
  font-size: $font-size-3xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
  margin: 0 0 $spacing-2 0;
  line-height: $line-height-tight;
}

.subtitle {
  font-size: $font-size-base;
  color: $gray-600;
  margin: 0;
  line-height: $line-height-normal;
}

.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-6;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.errorAlert {
  background-color: $error-50;
  border: 1px solid $error-200;
  border-radius: $border-radius;
  padding: $spacing-3 $spacing-4;
  color: $error-700;
  font-size: $font-size-sm;
  line-height: $line-height-normal;

  &::before {
    content: '⚠';
    margin-right: $spacing-2;
  }
}

.passwordToggle {
  background: none;
  border: none;
  cursor: pointer;
  color: $gray-400;
  padding: $spacing-1;
  border-radius: $border-radius-sm;
  transition: $transition-colors;

  &:hover {
    color: $gray-600;
    background-color: $gray-100;
  }

  &:focus {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
  }

  svg {
    width: $spacing-4;
    height: $spacing-4;
  }
}

.footer {
  margin-top: $spacing-6;
  text-align: center;
}

.footerText {
  font-size: $font-size-sm;
  color: $gray-600;
  margin: 0;
  line-height: $line-height-normal;
}

.link {
  background: none;
  border: none;
  color: $primary-600;
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  padding: 0;

  &:hover {
    color: $primary-700;
  }

  &:focus {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
    border-radius: $border-radius-sm;
  }
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 0;
}

.backgroundPattern {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {

  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }

  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }

  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

// Responsive adjustments
@include responsive(sm) {
  .loginContainer {
    padding: $spacing-6;
  }

  .title {
    font-size: $font-size-4xl;
  }
}

// Focus and accessibility improvements
.loginCard {
  &:focus-within {
    box-shadow: $shadow-2xl, 0 0 0 4px rgba($primary-500, 0.1);
  }
}

// Loading state
.form {
  &:has(button[disabled]) {
    pointer-events: none;
    opacity: 0.8;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .loginCard {
    border: 2px solid $gray-900;
  }

  .title {
    color: $gray-900;
  }

  .subtitle {
    color: $gray-700;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .backgroundPattern {
    animation: none;
  }

  * {
    transition: none !important;
  }
}