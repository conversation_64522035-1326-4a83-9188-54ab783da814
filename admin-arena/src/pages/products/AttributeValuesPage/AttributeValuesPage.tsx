// Attribute Values Page
// Inspired by Django template for managing attribute values

import React, { useState, useMemo } from 'react'
import { FiPlus, FiTrash2, FiEdit, FiSave, FiX, FiCheck } from 'react-icons/fi'
import Select from 'react-select'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  useAttributeValues,
  useCreateAttributeValue,
  useUpdateAttributeValue,
  useDeleteAttributeValue,
  useBulkCreateAttributeValues
} from '../../../hooks/products-hooks/use-attribute-values'
import { useAttributes } from '../../../hooks/products-hooks/use-attributes'
import { Card, CardHeader, CardBody } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Input } from '../../../components/ui/Input'
import { Switch } from '../../../components/ui/Switch'
import { Badge } from '../../../components/ui/Badge'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { PermissionGuard } from '../../../components/auth/AuthGuard'
import type { AttributeValue, AttributeValueCreateData } from '../../../types/api-types'
import styles from './AttributeValuesPage.module.scss'

// Zod schema for new attribute value
const newValueSchema = z.object({
  attribute_value: z.string().min(1, 'Value is required'),
  is_active: z.boolean(),
})

type NewValueFormData = z.infer<typeof newValueSchema>

interface NewAttributeValue extends NewValueFormData {
  id: string // temporary ID for UI
}

export const AttributeValuesPage: React.FC = () => {
  const [selectedAttributeId, setSelectedAttributeId] = useState<number | null>(null)
  const [editingValueId, setEditingValueId] = useState<number | null>(null)
  const [newValues, setNewValues] = useState<NewAttributeValue[]>([])

  // API hooks
  const { data: attributes } = useAttributes()
  const { data: existingValues, isLoading: isLoadingValues } = useAttributeValues(selectedAttributeId || undefined)
  const createValueMutation = useCreateAttributeValue()
  const updateValueMutation = useUpdateAttributeValue()
  const deleteValueMutation = useDeleteAttributeValue()
  const bulkCreateMutation = useBulkCreateAttributeValues()

  console.log(attributes)

  // Form setup for editing existing values
  const editForm = useForm<NewValueFormData>({
    resolver: zodResolver(newValueSchema),
  })

  // Attribute options for react-select
  const attributeOptions = attributes?.map(attr => ({
    value: attr.id,
    label: attr.title,
  })) || []

  // Handle attribute selection
  const handleAttributeChange = (option: any) => {
    setSelectedAttributeId(option ? option.value : null)
    setNewValues([])
    setEditingValueId(null)
    editForm.reset()
  }

  // Add new value row
  const addNewValueRow = () => {
    const newValue: NewAttributeValue = {
      id: `new_${Date.now()}`,
      attribute_value: '',
      is_active: true,
    }
    setNewValues(prev => [...prev, newValue])
  }

  // Remove new value row
  const removeNewValueRow = (id: string) => {
    setNewValues(prev => prev.filter(value => value.id !== id))
  }

  // Update new value
  const updateNewValue = (id: string, field: keyof NewValueFormData, value: any) => {
    setNewValues(prev => prev.map(val =>
      val.id === id ? { ...val, [field]: value } : val
    ))
  }

  // Save new values using bulk create
  const saveNewValues = async () => {
    if (!selectedAttributeId || newValues.length === 0) return

    const validValues = newValues.filter(val => val.attribute_value.trim() !== '')
    if (validValues.length === 0) return

    try {
      // Use bulk create for better performance
      const bulkCreateData = {
        attribute_id: selectedAttributeId,
        values: validValues.map(val => val.attribute_value)
      }
      await bulkCreateMutation.mutateAsync(bulkCreateData)
      setNewValues([])
    } catch (error) {
      // Error is handled by the mutation hook
    }
  }

  // Handle toggle active status directly
  const handleToggleActive = async (valueId: number, isActive: boolean) => {
    try {
      await updateValueMutation.mutateAsync({
        id: valueId,
        data: { is_active: isActive },
      })
    } catch (error) {
      // Error is handled by the mutation hook
    }
  }

  // Handle edit existing value
  const handleEditValue = (value: AttributeValue) => {
    setEditingValueId(value.id)
    editForm.setValue('attribute_value', value.attribute_value)
    editForm.setValue('is_active', value.is_active)
  }

  // Handle update existing value with dirty field tracking
  const handleUpdateValue = async (valueId: number) => {
    const formData = editForm.getValues()
    const dirtyFields = editForm.formState.dirtyFields

    // Only send fields that have been modified
    const updateData: Partial<NewValueFormData> = {}
    if (dirtyFields.attribute_value) {
      updateData.attribute_value = formData.attribute_value
    }
    if (dirtyFields.is_active) {
      updateData.is_active = formData.is_active
    }

    // If no fields are dirty, don't make the API call
    if (Object.keys(updateData).length === 0) {
      setEditingValueId(null)
      editForm.reset()
      return
    }

    try {
      await updateValueMutation.mutateAsync({
        id: valueId,
        data: updateData,
      })
      setEditingValueId(null)
      editForm.reset()
    } catch (error) {
      // Error is handled by the mutation hook
    }
  }

  // Handle delete value
  const handleDeleteValue = async (id: number) => {
    if (!selectedAttributeId) return

    if (window.confirm('Are you sure you want to delete this attribute value?')) {
      try {
        await deleteValueMutation.mutateAsync({ id, attributeId: selectedAttributeId })
      } catch (error) {
        // Error is handled by the mutation hook
      }
    }
  }

  // Cancel editing
  const cancelEditing = () => {
    setEditingValueId(null)
    editForm.reset()
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>Attribute Values</h1>
          <p className={styles.subtitle}>
            Select an attribute to view and manage its values
          </p>
        </div>
      </div>

      {/* Attribute Selection */}
      <Card className={styles.selectionCard}>
        <CardHeader>
          <h3>📋 Select Attribute</h3>
        </CardHeader>
        <CardBody>
          <div className={styles.selectionForm}>
            <div className={styles.formGroup}>
              <label htmlFor="attribute" className={styles.label}>
                Choose an attribute to view and manage its values
              </label>
              <Select
                id="attribute"
                options={attributeOptions}
                placeholder="Search and select an attribute..."
                classNamePrefix="react-select"
                value={attributeOptions.find(opt => opt.value === selectedAttributeId) || null}
                onChange={handleAttributeChange}
                styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Values Container */}
      {selectedAttributeId && (
        <div className={styles.valuesContainer}>
          {/* Existing Values */}
          <Card className={styles.existingCard}>
            <CardHeader>
              <h4>🔗 Existing Values</h4>
            </CardHeader>
            <CardBody>
              {isLoadingValues ? (
                <PageLoading message="Loading values..." />
              ) : existingValues && existingValues.length > 0 ? (
                <div className={styles.valuesList}>
                  {existingValues.map((value) => (
                    <div key={value.id} className={styles.valueRow}>
                      {editingValueId === value.id ? (
                        // Edit mode
                        <>
                          <div className={styles.valueInput}>
                            <Controller
                              name="attribute_value"
                              control={editForm.control}
                              render={({ field }) => (
                                <Input
                                  {...field}
                                  placeholder="Enter attribute value..."
                                  className={styles.editInput}
                                />
                              )}
                            />
                          </div>
                          <div className={styles.valueSettings}>
                            <div className={styles.settingItem}>
                              <label>Active</label>
                              <Controller
                                name="is_active"
                                control={editForm.control}
                                render={({ field }) => (
                                  <Switch
                                    checked={field.value}
                                    onChange={(e) => field.onChange(e.target.checked)}
                                  />
                                )}
                              />
                            </div>
                          </div>
                          <div className={styles.valueActions}>
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={() => handleUpdateValue(value.id)}
                              disabled={updateValueMutation.isPending}
                            >
                              <FiCheck />
                            </Button>
                            <Button
                              variant="secondary"
                              size="sm"
                              onClick={cancelEditing}
                            >
                              <FiX />
                            </Button>
                          </div>
                        </>
                      ) : (
                        // View mode
                        <>
                          <div className={styles.valueInfo}>
                            <span className={styles.valueText}>{value.attribute_value}</span>
                            {value.products_count !== undefined && (
                              <Badge variant="secondary" className={styles.countBadge}>
                                {value.products_count} products
                              </Badge>
                            )}
                          </div>
                          <div className={styles.valueSettings}>
                            <div className={styles.settingItem}>
                              <label>Active</label>
                              <Switch
                                checked={value.is_active}
                                onChange={(e) => handleToggleActive(value.id, e.target.checked)}
                                disabled={updateValueMutation.isPending}
                              />
                            </div>
                          </div>
                          <div className={styles.valueActions}>
                            <PermissionGuard permission="staff.change_attributevalue">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditValue(value)}
                                title="Edit value"
                              >
                                <FiEdit />
                              </Button>
                            </PermissionGuard>
                            <PermissionGuard permission="staff.delete_attributevalue">
                              <Button
                                variant="ghost"
                                size="sm"
                                className={styles.deleteButton}
                                onClick={() => handleDeleteValue(value.id)}
                                disabled={deleteValueMutation.isPending}
                                title="Delete value"
                              >
                                <FiTrash2 />
                              </Button>
                            </PermissionGuard>
                          </div>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className={styles.noValues}>
                  No existing values found.
                </div>
              )}
            </CardBody>
          </Card>

          {/* New Values */}
          <Card className={styles.newCard}>
            <CardHeader>
              <div className={styles.newCardHeader}>
                <h4>➕ Add New Values</h4>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={addNewValueRow}
                >
                  <FiPlus />
                  Add Value
                </Button>
              </div>
            </CardHeader>
            <CardBody>
              {newValues.length > 0 ? (
                <div className={styles.newValuesList}>
                  {newValues.map((value) => (
                    <div key={value.id} className={styles.newValueRow}>
                      <div className={styles.valueInput}>
                        <Input
                          value={value.attribute_value}
                          onChange={(e) => updateNewValue(value.id, 'attribute_value', e.target.value)}
                          placeholder="Enter attribute value..."
                        />
                      </div>
                      <div className={styles.valueSettings}>
                        <div className={styles.settingItem}>
                          <label>Active</label>
                          <Switch
                            checked={value.is_active}
                            onChange={(e) => updateNewValue(value.id, 'is_active', e.target.checked)}
                          />
                        </div>
                      </div>
                      <div className={styles.valueActions}>
                        <Button
                          variant="ghost"
                          size="sm"
                          className={styles.deleteButton}
                          onClick={() => removeNewValueRow(value.id)}
                          title="Remove value"
                        >
                          <FiX />
                        </Button>
                      </div>
                    </div>
                  ))}
                  <div className={styles.saveActions}>
                    <Button
                      variant="primary"
                      className={`${styles.submitButton} ${styles.primaryButton}`}
                      onClick={saveNewValues}
                      disabled={createValueMutation.isPending || newValues.every(val => val.attribute_value.trim() === '')}
                    >
                      <FiSave />
                      {createValueMutation.isPending ? 'Saving...' : 'Save Values'}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className={styles.noNewValues}>
                  Click "Add Value" to start adding values to this attribute.
                </div>
              )}
            </CardBody>
          </Card>
        </div>
      )}
    </div>
  )
}
