// Product variants management page
// Comprehensive variant management with SKU, pricing, and inventory

import React, { useState } from 'react'
import { useParams } from '@tanstack/react-router'
import { FiPlus, FiEdit, FiTrash2, FiArrowLeft, FiPackage } from 'react-icons/fi'
import { useProduct } from '../../../hooks/use-products'
import { useProductVariants, useCreateVariant, useDeleteVariant } from '../../../hooks/products-hooks/use-product-variants'
import { Card } from '../../../components/ui/Card'
import { Button, GhostButton } from '../../../components/ui/Button'
import { DataTable } from '../../../components/ui/DataTable'
import { Badge, StockStatusBadge } from '../../../components/ui/Badge'
import { Modal } from '../../../components/ui/Modal'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { PermissionGuard } from '../../../components/auth/AuthGuard'
import type { ProductVariant } from '../../../types/api-types'
import { formatCurrency } from '../../../utils/format'
import { VariantFormModal } from '../../../components/products/VariantFormModal'
import styles from './ProductVariantsPage.module.scss'

export const ProductVariantsPage: React.FC = () => {
  const { productId: productIdStr } = useParams({ from: '/products/$productId/edit' })
  const productId = parseInt(productIdStr, 10)
  
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingVariant, setEditingVariant] = useState<ProductVariant | null>(null)
  const [deletingVariant, setDeletingVariant] = useState<ProductVariant | null>(null)

  const { data: product, isLoading: productLoading } = useProduct(productId)
  const { data: variants, isLoading: variantsLoading, refetch } = useProductVariants(productId)
  const createVariant = useCreateVariant()
  const deleteVariantMutation = useDeleteVariant()

  const handleDeleteVariant = async () => {
    if (!deletingVariant) return
    
    try {
      await deleteVariantMutation.mutateAsync(deletingVariant.id)
      setDeletingVariant(null)
      await refetch()
    } catch (error) {
      console.error('Failed to delete variant:', error)
    }
  }

  const columns = [
    {
      key: 'sku',
      header: 'SKU',
      cell: (variant: ProductVariant) => (
        <div className={styles.skuCell}>
          <span className={styles.sku}>{variant.sku}</span>
        </div>
      ),
    },
    {
      key: 'attributes',
      header: 'Attributes',
      cell: (variant: ProductVariant) => (
        <div className={styles.attributesCell}>
          {variant.attribute_values?.map((attr, index) => (
            <Badge key={index} variant="secondary" size="sm">
              {attr.attribute_title}: {attr.attribute_value}
            </Badge>
          )) || <span className={styles.noAttributes}>No attributes</span>}
        </div>
      ),
    },
    {
      key: 'price',
      header: 'Price',
      cell: (variant: ProductVariant) => (
        <div className={styles.priceCell}>
          <span className={styles.price}>
            {formatCurrency(variant.price)}
          </span>
          {variant.price_label_title && (
            <span className={styles.comparePrice}>
              {variant.price_label_title}
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'inventory',
      header: 'Inventory',
      cell: (variant: ProductVariant) => (
        <div className={styles.inventoryCell}>
          <span className={styles.quantity}>{variant.stock_qty}</span>
          <StockStatusBadge quantity={variant.stock_qty} />
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      cell: (variant: ProductVariant) => (
        <Badge variant={variant.is_active ? 'success' : 'error'}>
          {variant.is_active ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (variant: ProductVariant) => (
        <div className={styles.actions}>
          <PermissionGuard permission="staff.change_productvariant">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingVariant(variant)}
              title="Edit variant"
            >
              <FiEdit />
            </Button>
          </PermissionGuard>
          
          <PermissionGuard permission="staff.delete_productvariant">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setDeletingVariant(variant)}
              title="Delete variant"
            >
              <FiTrash2 />
            </Button>
          </PermissionGuard>
        </div>
      ),
    },
  ]

  if (productLoading) {
    return <PageLoading message="Loading product..." />
  }

  if (!product) {
    return (
      <div className={styles.error}>
        <Card padding="lg">
          <h2>Product Not Found</h2>
          <p>The requested product could not be found.</p>
          <Button onClick={() => window.history.back()} variant="primary">
            Back to Products
          </Button>
        </Card>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <GhostButton
            onClick={() => window.history.back()}
            className={styles.backButton}
          >
            <FiArrowLeft /> Back to Products
          </GhostButton>
          <h1>Product Variants</h1>
          <Button
            onClick={() => setIsCreateModalOpen(true)}
            variant="primary"
            leftIcon={<FiPlus />}
          >
            Add Variant
          </Button>
        </div>
        <div className={styles.headerRight}>
          <p className={styles.subtitle}>
            Manage variants for "{product?.title || 'this product'}"
          </p>
          <PermissionGuard permission="staff.add_productvariant">
            <Button
              variant="primary"
              onClick={() => setIsCreateModalOpen(true)}
              className={styles.createButton}
            >
              <FiPlus />
              Add Variant
            </Button>
          </PermissionGuard>
        </div>
      </div>

      <div className={styles.productInfo}>
        <Card className={styles.productCard}>
          <div className={styles.productHeader}>
            <div className={styles.productIcon}>
              <FiPackage />
            </div>
            <div className={styles.productDetails}>
              <h3>{product?.title || 'Product'}</h3>
              <p>SKU: {product?.slug || 'N/A'}</p>
              <p>Category: {product?.category_title || 'N/A'}</p>
              <p>Brand: {product?.brand_title || 'N/A'}</p>
            </div>
            <div className={styles.productStats}>
              <div className={styles.stat}>
                <span className={styles.statValue}>{variants?.length || 0}</span>
                <span className={styles.statLabel}>Variants</span>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <Card>
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Variants</h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage product variants, pricing, and inventory</p>
        </div>
        <div className="p-4">
          <DataTable
            data={variants || []}
            columns={columns}
            loading={variantsLoading}
            emptyMessage="No variants found. Create your first variant to get started."
          />
        </div>
      </Card>

      {/* Create Variant Modal */}
      <VariantFormModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        productId={productId}
        mode="create"
        onSuccess={() => {
          setIsCreateModalOpen(false)
          refetch()
        }}
      />

      {/* Edit Variant Modal */}
      {editingVariant && (
        <VariantFormModal
          isOpen={true}
          onClose={() => setEditingVariant(null)}
          productId={productId}
          variant={editingVariant}
          mode="edit"
          onSuccess={() => {
            setEditingVariant(null)
            refetch()
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingVariant}
        onClose={() => setDeletingVariant(null)}
        title="Delete Variant"
        size="sm"
      >
        <div className={styles.deleteModal}>
          <p>
            Are you sure you want to delete the variant "{deletingVariant?.sku}"? 
            This action cannot be undone.
          </p>
          
          <div className={styles.deleteActions}>
            <Button
              variant="outline"
              onClick={() => setDeletingVariant(null)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleDeleteVariant}
              disabled={deleteVariantMutation.isPending}
            >
              {deleteVariantMutation.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}
