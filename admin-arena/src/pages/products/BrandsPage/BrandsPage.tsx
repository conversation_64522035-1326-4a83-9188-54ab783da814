// Brands management page
// Complete CRUD operations for product brands

import React, { useState } from 'react'
import { FiPlus, FiEdit, FiTrash2, FiSearch } from 'react-icons/fi'
import {
  useProductBrands,
  useCreateBrand,
  useUpdateBrand,
  useDeleteBrand,
} from '../../../hooks/products-hooks/use-brands'

import { Card, CardHeader, CardBody } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Input } from '../../../components/ui/Input'
import { Modal } from '../../../components/ui/Modal'
import { Badge } from '../../../components/ui/Badge'
import { DataTable } from '../../../components/ui/DataTable'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { PermissionGuard } from '../../../components/auth/AuthGuard'
import { Switch } from '../../../components/ui/Switch'
import type { Brand, ProductType } from '../../../types/api-types'
import styles from './BrandsPage.module.scss'
import Select from 'react-select'

export const BrandsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingBrand, setEditingBrand] = useState<Brand | null>(null)
  const [deletingBrand, setDeletingBrand] = useState<Brand | null>(null)
  const [brandTitle, setBrandTitle] = useState('')
  const [brandSlug, setBrandSlug] = useState('')
  const [brandIsActive, setBrandIsActive] = useState(true)
  const [slugAuto, setSlugAuto] = useState(true)

  const { data: brands, isLoading, refetch } = useProductBrands()
  const createBrandMutation = useCreateBrand()
  const updateBrandMutation = useUpdateBrand()
  const deleteBrandMutation = useDeleteBrand()

  const filteredBrands = brands?.filter(brand =>
    brand.title.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []



  const handleCreateBrand = async () => {
    if (!brandTitle.trim()) return

    try {
      const brandData = {
        title: brandTitle,
        slug: brandSlug.trim() || undefined,
        is_active: brandIsActive
      }

      await createBrandMutation.mutateAsync(brandData)
      resetForm()
      setIsCreateModalOpen(false)
      refetch()
    } catch (error) {
      console.error('Failed to create brand:', error)
    }
  }

  const handleUpdateBrand = async () => {
    if (!editingBrand || !brandTitle.trim()) return

    try {
      const brandData = {
        title: brandTitle,
        slug: brandSlug.trim() || undefined,
        is_active: brandIsActive
      }

      await updateBrandMutation.mutateAsync({
        id: editingBrand.id,
        data: brandData
      })

      resetForm()
      setEditingBrand(null)
      refetch()
    } catch (error) {
      console.error('Failed to update brand:', error)
    }
  }

  const handleDeleteBrand = async () => {
    if (!deletingBrand) return

    try {
      await deleteBrandMutation.mutateAsync(deletingBrand.id)
      setDeletingBrand(null)
      refetch()
    } catch (error) {
      console.error('Failed to delete brand:', error)
    }
  }

  const resetForm = () => {
    setBrandTitle('')
    setBrandSlug('')
    setBrandIsActive(true)
  }

  const handleBrandTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBrandTitle(e.target.value)
    if (slugAuto) {
      setBrandSlug(
        e.target.value
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)+/g, '')
      )
    }
  }

  const openEditModal = (brand: Brand) => {
    setEditingBrand(brand)
    setBrandTitle(brand.title)
    setBrandSlug(brand.slug || '')
    setBrandIsActive(brand.is_active ?? true)
    setSlugAuto(false)
  }

  const columns: Array<{
    key: string
    header: string
    render?: (value: any, brand: Brand, index: number) => React.ReactNode
    width?: string
  }> = [
      {
        key: 'title',
        header: 'Brand Name',
        render: (value: any, brand: Brand) => (
          <div className={styles.brandInfo}>
            <h4 className={styles.brandName}>{brand.title}</h4>
            {/* {brand.slug && (
              <p className={styles.brandSlug}>{brand.slug}</p>
            )} */}
          </div>
        ),
      },
      {
        key: 'products_count',
        header: 'Products',
        render: (value: any, brand: Brand) => (
          <Badge variant="secondary">
            {brand.products_count || 0} products
          </Badge>
        ),
      },
      {
        key: 'is_active',
        header: 'Status',
        render: (value: any, brand: Brand) => (
          <Badge variant={brand.is_active ? 'success' : 'secondary'}>
            {brand.is_active ? 'Active' : 'Inactive'}
          </Badge>
        ),
      },
      {
        key: 'actions',
        header: 'Actions',
        render: (value: any, brand: Brand) => (
          <div className={styles.actions}>
            <PermissionGuard permission="staff.change_brand">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => openEditModal(brand)}
                title="Edit brand"
              >
                <FiEdit />
              </Button>
            </PermissionGuard>

            <PermissionGuard permission="staff.delete_brand">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDeletingBrand(brand)}
                title="Delete brand"
                disabled={(brand.products_count || 0) > 0}
              >
                <FiTrash2 />
              </Button>
            </PermissionGuard>
          </div>
        ),
        width: '120px',
      },
    ]

  if (isLoading) {
    return <PageLoading message="Loading brands..." />
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>Brands</h1>
          <p className={styles.subtitle}>
            Manage product brands and their information
          </p>
        </div>

        <div className={styles.actions}>
          <PermissionGuard permission="staff.add_brand">
            <Button
              variant="primary"
              onClick={() => setIsCreateModalOpen(true)}
              className={styles.createButton}
            >
              <FiPlus />
              Add Brand
            </Button>
          </PermissionGuard>
        </div>
      </div>

      <Card className={styles.brandsCard}>
        <CardHeader>
          <div className={styles.cardHeader}>
            <h2>All Brands</h2>
            <div className={styles.searchContainer}>
              <div className={styles.searchBox}>
                <FiSearch className={styles.searchIcon} />
                <Input
                  type="text"
                  placeholder="Search brands..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={styles.searchInput}
                />
              </div>
            </div>
          </div>
        </CardHeader>

        <CardBody>
          <DataTable
            data={filteredBrands}
            columns={columns}
            emptyMessage="No brands found"
          />
        </CardBody>
      </Card>

      {/* Create/Edit Brand Modal */}
      <Modal
        isOpen={isCreateModalOpen || !!editingBrand}
        onClose={() => {
          setIsCreateModalOpen(false)
          setEditingBrand(null)
          resetForm()
          setSlugAuto(true)
        }}
        title={editingBrand ? 'Edit Brand' : 'Create New Brand'}
        size="lg"
      >
        <div className={styles.brandModal}>
          <div className={styles.formGroup}>
            <label htmlFor="brandTitle">Brand Name *</label>
            <Input
              id="brandTitle"
              type="text"
              value={brandTitle}
              onChange={handleBrandTitleChange}
              onFocus={() => setSlugAuto(true)}
              onBlur={() => setSlugAuto(false)}
              placeholder="Enter brand name"
              disabled={createBrandMutation.isPending || updateBrandMutation.isPending}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="brandSlug">Brand Slug</label>
            <Input
              id="brandSlug"
              type="text"
              value={brandSlug}
              onChange={e => { setBrandSlug(e.target.value); setSlugAuto(false) }}
              placeholder="Enter brand slug (optional)"
              disabled={createBrandMutation.isPending || updateBrandMutation.isPending}
            />
            <small>Auto-generated from brand name, but you can edit it.</small>
          </div>

          <div className={styles.formGroup}>
            <div className={styles.rowAlign}>
              <label htmlFor="brandIsActive">Active Status</label>
              <Switch
                id="brandIsActive"
                checked={brandIsActive}
                onChange={(e) => setBrandIsActive(e.target.checked)}
                disabled={createBrandMutation.isPending || updateBrandMutation.isPending}
              />
              <span>{brandIsActive ? 'Active' : 'Inactive'}</span>
            </div>
          </div>

          <div className={styles.formGroup}>
            {/* <div className={styles.rowAlign}>
              <label htmlFor="productTypes">Product Types</label>
              <Select
                id="productTypes"
                isMulti
                isClearable
                options={filteredProductTypes.map(pt => ({ value: pt.id, label: pt.title }))}
                value={filteredProductTypes.filter(pt => selectedProductTypes.includes(pt.id)).map(pt => ({ value: pt.id, label: pt.title }))}
                onChange={opts => setSelectedProductTypes(Array.isArray(opts) ? opts.map(opt => opt.value) : [])}
                placeholder="Select product types..."
                classNamePrefix="react-select"
                styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
              />
              <small>Hold Ctrl/Cmd to select multiple product types</small>
            </div> */}
          </div>

          {/* <div className={styles.formGroup}>
            <label htmlFor="brandLogo">Brand Logo (Optional)</label>
            <div className={styles.logoUpload}>
              <input
                id="brandLogo"
                type="file"
                accept="image/*"
                // onChange={handleLogoChange}
                className={styles.fileInput}
              />
              <label htmlFor="brandLogo" className={styles.uploadArea}>
                {logoPreview ? (
                  <img src={logoPreview} alt="Logo preview" className={styles.logoPreview} />
                ) : (
                  <div className={styles.uploadPlaceholder}>
                    <FiImage />
                    <span>Click to upload logo</span>
                  </div>
                )}
              </label>
            </div>
          </div> */}

          <div className={styles.modalActions}>
            <Button
              variant="outline"
              onClick={() => {
                setIsCreateModalOpen(false)
                setEditingBrand(null)
                resetForm()
                setSlugAuto(true)
              }}
              disabled={createBrandMutation.isPending || updateBrandMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={editingBrand ? handleUpdateBrand : handleCreateBrand}
              disabled={
                !brandTitle.trim() ||
                createBrandMutation.isPending ||
                updateBrandMutation.isPending
              }
            >
              {createBrandMutation.isPending || updateBrandMutation.isPending
                ? (editingBrand ? 'Updating...' : 'Creating...')
                : (editingBrand ? 'Update Brand' : 'Create Brand')
              }
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingBrand}
        onClose={() => setDeletingBrand(null)}
        title="Delete Brand"
        size="sm"
      >
        <div className={styles.deleteModal}>
          <p>
            Are you sure you want to delete the brand "{deletingBrand?.title}"?
            This action cannot be undone.
          </p>

          <div className={styles.deleteActions}>
            <Button
              variant="outline"
              onClick={() => setDeletingBrand(null)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleDeleteBrand}
              disabled={deleteBrandMutation.isPending}
            >
              {deleteBrandMutation.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}
