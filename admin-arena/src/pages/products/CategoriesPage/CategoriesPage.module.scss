@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  padding: $spacing-6;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  @include flex-between;
  margin-bottom: $spacing-6;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.titleSection {
  flex: 1;

  .title {
    margin: 0 0 $spacing-2 0;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $gray-900;
  }

  .subtitle {
    margin: 0;
    color: $gray-600;
    font-size: $font-size-base;
  }
}

.actions {
  @include flex-start;
  gap: $spacing-3;
}

.createButton {
  @include flex-center;
  gap: $spacing-2;
}

.categoriesCard {
  @include card;
}

.cardHeader {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-4;
  padding: $spacing-6;
  border-bottom: 1px solid $gray-200;

  h2 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0 0 $spacing-1 0;
  }

  p {
    font-size: $font-size-sm;
    color: $gray-600;
    margin: 0;
  }

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.treeStats {
  @include flex-start;
  gap: $spacing-2;
}

.categoriesBody {
  padding: 0;
}

// MPTT-style tree structure
.categoryTree {
  @include flex-column;
}

.categoryItem {
  display: flex;
  align-items: center;
  border-bottom: 1px solid $gray-100;
  padding: 0 $spacing-2;
  min-height: 36px;
  background: none;
  cursor: pointer;
  transition: background 0.15s, border 0.15s;

  &:hover {
    background: $gray-25;
  }

  &.dragOver {
    background: $primary-50;
    border-left: 3px solid $primary-400;
  }
}

.categoryRow {
  @include flex-start;
  align-items: center;
  gap: $spacing-2;
  padding: $spacing-2 $spacing-2;
  min-height: 36px;
  transition: background-color 0.15s;

  &:hover {
    background-color: $gray-25;

    .categoryActions {
      opacity: 1;
    }
  }
}

.expandContainer {
  width: $spacing-6;
  @include flex-center;
}

.expandButton {
  @include flex-center;
  width: $spacing-6;
  height: $spacing-6;
  border: none;
  background: none;
  color: $gray-500;
  cursor: pointer;
  border-radius: $border-radius-sm;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: $gray-100;
    color: $gray-700;
  }

  svg {
    font-size: $font-size-sm;
  }
}

.expandPlaceholder {
  width: $spacing-6;
  height: $spacing-6;
}

.categoryIcon {
  @include flex-center;
  width: $spacing-8;
  height: $spacing-8;
  background: $primary-50;
  color: $primary-600;
  border-radius: $border-radius;
  font-size: $font-size-base;
  flex-shrink: 0;
  margin-right: $spacing-2;
}

.categoryInfo {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: $spacing-2;
}

.categoryName {
  font-weight: $font-weight-medium;
  color: $gray-900;
  font-size: $font-size-base;
  margin-right: $spacing-2;
}

.categoryDescription {
  font-size: $font-size-sm;
  color: $gray-600;
  margin: 0;
  @include line-clamp(2);
}

.productCount {
  @include flex-center;
  margin-right: $spacing-4;
}

.categoryActions {
  display: flex;
  align-items: center;
  gap: $spacing-1;
  opacity: 0.7;
  transition: opacity 0.2s;
  margin-left: $spacing-2;

  &:hover,
  .categoryItem:hover & {
    opacity: 1;
  }
}

.actionButton {
  @include flex-center;
  width: $spacing-6;
  height: $spacing-6;
  border: none;
  background: none;
  color: $gray-500;
  cursor: pointer;
  border-radius: $border-radius-sm;
  font-size: $font-size-base;
  transition: background 0.15s, color 0.15s;

  &:hover {
    background: $gray-100;
    color: $primary-600;
  }

  &.deleteButton {
    &:hover {
      background-color: $error-50;
      color: $error-600;
    }
  }

  svg {
    font-size: $font-size-sm;
  }
}

.childrenContainer {
  background-color: $gray-25;
}

// Empty state
.emptyState {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-12;
  color: $gray-500;
  text-align: center;

  svg {
    font-size: $font-size-4xl;
    color: $gray-300;
  }

  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-700;
    margin: 0;
  }

  p {
    font-size: $font-size-sm;
    margin: 0;
  }
}

// Modal form styles
.form {
  @include flex-column;
  gap: $spacing-4;
}

.formGroup {
  @include flex-column;
  gap: $spacing-2;
}

.label {
  font-weight: $font-weight-medium;
  color: $gray-700;
  font-size: $font-size-sm;
}

.parentSelect {
  @include input-base;
}

.helpText {
  font-size: $font-size-xs;
  color: $gray-500;
  line-height: $line-height-normal;
}

.formActions {
  @include flex-end;
  gap: $spacing-3;
  margin-top: $spacing-2;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
}

// Delete confirmation modal
.deleteModal {
  .deleteContent {
    @include flex-column;
    gap: $spacing-4;
    text-align: center;

    .deleteIcon {
      @include flex-center;
      width: $spacing-16;
      height: $spacing-16;
      background: $error-100;
      color: $error-600;
      border-radius: $border-radius-full;
      font-size: $font-size-2xl;
      margin: 0 auto;
    }

    .deleteTitle {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $gray-900;
      margin: 0;
    }

    .deleteMessage {
      color: $gray-600;
      font-size: $font-size-sm;
      margin: 0;
      line-height: $line-height-relaxed;
    }

    .deleteActions {
      @include flex-center;
      gap: $spacing-3;
      margin-top: $spacing-2;
    }
  }
}

// Empty state
.emptyState {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-12;
  color: $gray-500;
  text-align: center;

  svg {
    font-size: $font-size-4xl;
    color: $gray-300;
  }

  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-700;
    margin: 0;
  }

  p {
    font-size: $font-size-sm;
    margin: 0;
  }
}

// Modal form styles
.form {
  @include flex-column;
  gap: $spacing-4;
}

.formGroup {
  @include flex-column;
  gap: $spacing-2;
}

.label {
  font-weight: $font-weight-medium;
  color: $gray-700;
  font-size: $font-size-sm;
}

.parentSelect {
  @include input-base;
}

.formActions {
  @include flex-end;
  gap: $spacing-3;
  margin-top: $spacing-2;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
}

// Delete confirmation modal
.deleteModal {
  .deleteContent {
    @include flex-column;
    gap: $spacing-4;
    text-align: center;

    .deleteIcon {
      @include flex-center;
      width: $spacing-16;
      height: $spacing-16;
      background: $error-100;
      color: $error-600;
      border-radius: $border-radius-full;
      font-size: $font-size-2xl;
      margin: 0 auto;
    }

    .deleteTitle {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $gray-900;
      margin: 0;
    }

    .deleteMessage {
      color: $gray-600;
      font-size: $font-size-sm;
      margin: 0;
      line-height: $line-height-relaxed;
    }

    .deleteActions {
      @include flex-center;
      gap: $spacing-3;
      margin-top: $spacing-2;
    }
  }
}

.movingSpinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid $primary-200;
  border-top: 2px solid $primary-500;
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
  margin-left: 4px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Reduce left padding for nested levels
.categoryItem {
  padding-left: 0 !important;
}