// Categories management page
// Hierarchical category management with tree structure and drag-and-drop

import React, { useState, useMemo } from 'react'
import { FiPlus, FiEdit, FiTrash2, FiFolder, FiFolderPlus, FiChevronRight, FiChevronDown } from 'react-icons/fi'
import { useProductCategories, useCreateCategory, useUpdateCategory, useDeleteCategory } from '../../../hooks/products-hooks/use-categories'
import { Card } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Input } from '../../../components/ui/Input'
import { Modal } from '../../../components/ui/Modal'
import { Badge } from '../../../components/ui/Badge'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { PermissionGuard } from '../../../components/auth/AuthGuard'
import type { Category } from '../../../types/api-types'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import styles from './CategoriesPage.module.scss'
import { useDrop, useDrag, DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { useNotifications } from '../../../stores/ui-store'
import Select from 'react-select'
import { Switch } from '../../../components/ui/Switch'

// Zod schema for category form
const categorySchema = z.object({
  title: z.string().min(1, 'Category title is required'),
  slug: z.string().min(1, 'Slug is required'),
  parent: z.number().nullable().optional(),
  is_active: z.boolean().optional(),
})

type CategoryFormData = z.infer<typeof categorySchema>

// Helper for drag-and-drop type
const DND_ITEM_TYPE = 'CATEGORY'

/**
 * Helper to move a category to a new parent via API
 */
async function moveCategoryAPI(categoryId: number, newParentId: number | null) {
  // PATCH /api/staff/products/categories/{id}/move/
  const response = await fetch(`/api/staff/products/categories/${categoryId}/move/`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ new_parent_id: newParentId }),
  })
  if (!response.ok) {
    throw new Error('Failed to move category')
  }
  return response.json()
}

/**
 * Categories management page with hierarchical tree and drag-and-drop
 */
export const CategoriesPage: React.FC = () => {
  const [expandedCategories, setExpandedCategories] = useState<Set<number>>(new Set())
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [deletingCategory, setDeletingCategory] = useState<Category | null>(null)
  const [selectedParentId, setSelectedParentId] = useState<number | null>(null)
  const { data: categories = [], isLoading, refetch } = useProductCategories()
  const createCategoryMutation = useCreateCategory()
  const updateCategoryMutation = useUpdateCategory()
  const deleteCategoryMutation = useDeleteCategory()

  // Get mutation pending states
  const isFormPending = createCategoryMutation.isPending || updateCategoryMutation.isPending
  const [movingCategoryId, setMovingCategoryId] = useState<number | null>(null)
  const { showSuccess, showError } = useNotifications()
  const [slugAuto, setSlugAuto] = useState(true)

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      title: '',
      slug: '',
      parent: null,
      is_active: true,
    },
  })
  const titleValue = watch('title')
  const slugValue = watch('slug')
  React.useEffect(() => {
    if (slugAuto) {
      setValue('slug',
        titleValue
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)+/g, '')
      )
    }
  }, [titleValue, slugAuto, setValue])

  // Build hierarchical tree structure from flat list
  const categoryTree = useMemo(() => {
    const buildTree = (parentId: number | null = null): (Category & { children: Category[] })[] => {
      return categories
        .filter(cat => (cat.parent ?? null) === parentId)
        .sort((a, b) => a.title.localeCompare(b.title))
        .map(category => ({
          ...category,
          children: buildTree(category.id),
        }))
    }
    return buildTree()
  }, [categories])

  // Flatten tree for dropdown
  const flatCategories = useMemo(() => {
    const flatten = (cats: (Category & { children?: Category[] })[], level = 0): Array<Category & { level: number }> => {
      return cats.reduce((acc, cat) => {
        acc.push({ ...cat, level })
        if (cat.children && cat.children.length > 0) {
          acc.push(...flatten(cat.children, level + 1))
        }
        return acc
      }, [] as Array<Category & { level: number }>)
    }
    return flatten(categoryTree)
  }, [categoryTree])

  // Expand/collapse helpers
  const toggleExpanded = (categoryId: number) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev)
      if (newSet.has(categoryId)) newSet.delete(categoryId)
      else newSet.add(categoryId)
      return newSet
    })
  }
  const expandAll = () => setExpandedCategories(new Set(categories.map(cat => cat.id)))
  const collapseAll = () => setExpandedCategories(new Set())

  // Modal helpers
  const openCreateModal = (parentId: number | null = null) => {
    setSlugAuto(true)
    setSelectedParentId(parentId)
    reset({ title: '', slug: '', parent: parentId, is_active: true })
    setIsCreateModalOpen(true)
  }
  const openEditModal = (category: Category) => {
    setSlugAuto(false)
    setEditingCategory(category)
    reset({
      title: category.title,
      slug: category.slug || '',
      parent: category.parent ?? null,
      is_active: category.is_active ?? true,
    })
  }

  // Create/update/delete handlers
  const handleCreateCategory = async (data: CategoryFormData) => {
    try {
      await createCategoryMutation.mutateAsync({
        title: data.title,
        slug: data.slug,
        parent: data.parent || null,
        is_active: data.is_active ?? true,
      })
      setIsCreateModalOpen(false)
      reset()
      // refetch() // Ensure categories list is refreshed
    } catch (error) {
      console.error('Failed to create category:', error)
    }
  }
  const handleUpdateCategory = async (data: CategoryFormData) => {
    if (!editingCategory) return
    try {
      await updateCategoryMutation.mutateAsync({
        id: editingCategory.id,
        data: {
          title: data.title,
          slug: data.slug,
          parent: data.parent || null,
          is_active: data.is_active ?? true,
        },
      })
      setEditingCategory(null)
      reset()
      refetch() // Ensure categories list is refreshed
    } catch (error) {
      console.error('Failed to update category:', error)
    }
  }
  const handleDeleteCategory = async () => {
    if (!deletingCategory) return
    try {
      await deleteCategoryMutation.mutateAsync(deletingCategory.id)
      setDeletingCategory(null)
      refetch()
    } catch (error) {
      console.error('Failed to delete category:', error)
    }
  }

  // Drag-and-drop logic for category tree
  /**
   * CategoryTreeItem: Renders a single category row with drag-and-drop
   */
  const CategoryTreeItem: React.FC<{
    category: Category & { children?: Category[] }
    level: number
  }> = ({ category, level }) => {
    const hasChildren = category.children && category.children.length > 0
    const isExpanded = expandedCategories.has(category.id)
    const productCount = category.products_count || 0
    // Drag source
    const [{ isDragging }, dragRef] = useDrag({
      type: DND_ITEM_TYPE,
      item: { id: category.id },
      collect: monitor => ({ isDragging: monitor.isDragging() }),
      canDrag: () => true,
    })
    // Drop target
    const [{ isOver, canDrop }, dropRef] = useDrop({
      accept: DND_ITEM_TYPE,
      canDrop: (item: { id: number }) => item.id !== category.id && item.id !== category.parent,
      drop: async (item: { id: number }) => {
        setMovingCategoryId(item.id)
        try {
          await moveCategoryAPI(item.id, category.id)
          showSuccess('Category moved', 'Category moved successfully.')
          refetch()
        } catch (e) {
          showError('Move failed', e instanceof Error ? e.message : 'Failed to move category.')
        } finally {
          setMovingCategoryId(null)
        }
      },
      collect: monitor => ({
        isOver: monitor.isOver({ shallow: true }),
        canDrop: monitor.canDrop(),
      }),
    })
    // Combine drag and drop refs
    const dragDropRef = (node: HTMLDivElement | null) => {
      dragRef(node)
      dropRef(node)
    }
    // Show spinner if this row is being moved
    const isMoving = movingCategoryId === category.id
    return (
      <>
        <div
          ref={dragDropRef}
          className={styles.categoryItem}
          style={{
            paddingLeft: `${level * 16 + 8}px`, // less indent
            opacity: isDragging || isMoving ? 0.5 : 1,
            background: isOver && canDrop ? '#e6f7ff' : undefined,
            cursor: 'pointer',
          }}
        >
          {/* Expand/Collapse Button */}
          <div className={styles.expandContainer}>
            {hasChildren ? (
              <button
                className={styles.expandButton}
                onClick={() => toggleExpanded(category.id)}
                aria-label={isExpanded ? 'Collapse' : 'Expand'}
              >
                {isExpanded ? <FiChevronDown /> : <FiChevronRight />}
              </button>
            ) : (
              <div className={styles.expandPlaceholder} />
            )}
          </div>
          {/* Category Icon */}
          <div className={styles.categoryIcon}>
            {hasChildren ? (
              isExpanded ? <FiFolderPlus /> : <FiFolder />
            ) : (
              <FiFolder />
            )}
          </div>
          {/* Category Info */}
          <div className={styles.categoryInfo}>
            <div className={styles.categoryName}>
              {category.title}
              {!category.is_active && (
                <Badge variant="secondary" size="sm">Inactive</Badge>
              )}
            </div>
            {category.description && (
              <div className={styles.categoryDescription}>
                {category.description}
              </div>
            )}
          </div>
          {/* Product Count */}
          <div className={styles.productCount}>
            <Badge variant="secondary">
              {productCount} product{productCount !== 1 ? 's' : ''}
            </Badge>
          </div>
          {/* Actions */}
          <div className={styles.categoryActions}>
            <button
              className={styles.actionButton}
              onClick={() => openCreateModal(category.id)}
              title="Add subcategory"
            >
              <FiPlus />
            </button>
            <button
              className={styles.actionButton}
              onClick={() => openEditModal(category)}
              title="Edit category"
            >
              <FiEdit />
            </button>
            <button
              className={`${styles.actionButton} ${styles.deleteButton}`}
              onClick={() => setDeletingCategory(category)}
              title="Delete category"
            >
              <FiTrash2 />
            </button>
            {isMoving && <span className={styles.movingSpinner}></span>}
          </div>
        </div>
        {/* Children */}
        {hasChildren && isExpanded && (
          <div className={styles.childrenContainer}>
            {category.children!.map(child => (
              <CategoryTreeItem
                key={child.id}
                category={child}
                level={level + 1}
              />
            ))}
          </div>
        )}
      </>
    )
  }

  if (isLoading) {
    return <PageLoading message="Loading categories..." />
  }

  // For react-select parent dropdown
  const parentOptions = flatCategories
    .filter(cat => editingCategory ? cat.id !== editingCategory.id : true)
    .map(category => ({
      value: category.id,
      label: `${'—'.repeat(category.level)} ${category.title}`,
    }))

  return (
    <DndProvider backend={HTML5Backend}>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.titleSection}>
            <h1 className={styles.title}>Categories</h1>
            <p className={styles.subtitle}>
              Organize your products with hierarchical categories
            </p>
          </div>
          <div className={styles.actions}>
            <Button
              variant="outline"
              size="sm"
              onClick={expandAll}
              disabled={categories.length === 0}
            >
              Expand All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={collapseAll}
              disabled={categories.length === 0}
            >
              Collapse All
            </Button>
            <PermissionGuard permission="staff.add_category">
              <Button
                variant="primary"
                onClick={() => openCreateModal()}
                className={styles.createButton}
              >
                <FiPlus />
                Add Category
              </Button>
            </PermissionGuard>
          </div>
        </div>
        <Card className={styles.categoriesCard}>
          <div className={styles.cardHeader}>
            <div>
              <h2>Category Tree</h2>
              <p>Manage your product categories and their hierarchy</p>
            </div>
            <div className={styles.treeStats}>
              <Badge variant="secondary">
                {categories.length} total categories
              </Badge>
            </div>
          </div>
          <div className={styles.categoriesBody}>
            {categoryTree.length > 0 ? (
              <div className={styles.categoryTree}>
                {categoryTree.map(category => (
                  <CategoryTreeItem
                    key={category.id}
                    category={category}
                    level={0}
                  />
                ))}
              </div>
            ) : (
              <div className={styles.emptyState}>
                <FiFolder />
                <h3>No categories found</h3>
                <p>Create your first category to organize your products</p>
                <Button
                  variant="primary"
                  onClick={() => openCreateModal()}
                >
                  <FiPlus />
                  Create Category
                </Button>
              </div>
            )}
          </div>
        </Card>
        {/* Create/Edit Category Modal */}
        <Modal
          isOpen={isCreateModalOpen || !!editingCategory}
          onClose={() => {
            setIsCreateModalOpen(false)
            setEditingCategory(null)
            reset()
          }}
          title={editingCategory ? 'Edit Category' : 'Create New Category'}
          size="lg"
        >
          <form
            onSubmit={handleSubmit(editingCategory ? handleUpdateCategory : handleCreateCategory)}
            className={styles.form}
          >
            <div className={styles.formGroup}>
              <label htmlFor="title" className={styles.label}>
                Category Title *
              </label>
              <Input
                id="title"
                {...register('title')}
                placeholder="Enter category title"
                error={errors.title?.message}
                onFocus={() => setSlugAuto(true)}
                onBlur={() => setSlugAuto(false)}
                disabled={isFormPending}
              />
              <span className='form__help_text'>E.g.: Hard Drives, CPUs (make it Capitalized or Uppercase and plural exactly as you want to show it in the frontend)</span>
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="slug" className={styles.label}>
                Slug *
              </label>
              <Input
                id="slug"
                {...register('slug')}
                placeholder="Enter slug"
                error={errors.slug?.message}
                onChange={e => {
                  setSlugAuto(false)
                  setValue('slug', e.target.value)
                }}
                disabled={isFormPending}
              />
              <small>Auto-generated from title, but you can edit it.</small>
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="parent" className={styles.label}>
                Parent Category (Optional)
              </label>
              <Select
                id="parent"
                options={parentOptions}
                isClearable
                placeholder="No parent (top-level category)"
                value={parentOptions.find(opt => opt.value === watch('parent')) || null}
                onChange={opt => setValue('parent', opt ? opt.value : null)}
                classNamePrefix="react-select"
                isDisabled={isFormPending}
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="is_active" className={styles.label}>
                Active
              </label>
              <Switch
                id="is_active"
                checked={!!watch('is_active')}
                onChange={e => setValue('is_active', e.target.checked)}
                disabled={isFormPending}
              />
            </div>
            <div className={styles.formActions}>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsCreateModalOpen(false)
                  setEditingCategory(null)
                  reset()
                }}
                disabled={isFormPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                className={`${styles.submitButton} ${styles.primaryButton}`}
                disabled={isFormPending}
              >
                {isFormPending ? (
                  editingCategory ? 'Updating Category...' : 'Creating Category...'
                ) : (
                  editingCategory ? 'Update Category' : 'Create Category'
                )}
              </Button>
            </div>
          </form>
        </Modal>
        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={!!deletingCategory}
          onClose={() => setDeletingCategory(null)}
          title="Delete Category"
          size="sm"
        >
          <div className={styles.deleteModal}>
            <p>
              Are you sure you want to delete the category "{deletingCategory?.title}"?
              This action cannot be undone.
            </p>
            <div className={styles.deleteActions}>
              <Button
                variant="outline"
                onClick={() => setDeletingCategory(null)}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleDeleteCategory}
                disabled={deleteCategoryMutation.isPending}
              >
                {deleteCategoryMutation.isPending ? 'Deleting...' : 'Delete'}
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    </DndProvider>
  )
}
