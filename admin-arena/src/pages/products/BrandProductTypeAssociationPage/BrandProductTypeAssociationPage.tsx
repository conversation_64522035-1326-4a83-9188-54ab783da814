// Brand-Product Type Association Page
// Allows users to associate brands with product types

import React, { useState, useMemo } from 'react'
import { FiPlus, FiSearch, FiLink, FiEdit } from 'react-icons/fi'
import Select from 'react-select'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useBrandProductTypeAssociations } from '../../../hooks/products-hooks/use-brands-product-type'
import {
  useAssociateBrandsWithProductType,
  useManageProductTypeBrands,
  useProductTypeBrands
} from '../../../hooks/products-hooks/use-brands-product-type'
import { useProductBrands } from '../../../hooks/products-hooks/use-brands'
import { useProductTypes } from '../../../hooks/products-hooks/use-product-types'
import { Card, CardHeader, CardBody } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Input } from '../../../components/ui/Input'
import { DataTable } from '../../../components/ui/DataTable'
import { Badge } from '../../../components/ui/Badge'
import { Modal } from '../../../components/ui/Modal'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { PermissionGuard } from '../../../components/auth/AuthGuard'
import styles from './BrandProductTypeAssociationPage.module.scss'

// Zod schema for association form (create)
const associationSchema = z.object({
  product_type: z.number().min(1, 'Please select a product type'),
  brands: z.array(z.number()).min(1, 'Please select at least one brand'),
})

// Zod schema for edit form (allows empty brands)
const editAssociationSchema = z.object({
  product_type: z.number().min(1, 'Please select a product type'),
  brands: z.array(z.number()), // No minimum requirement for edit
})

type AssociationFormData = z.infer<typeof associationSchema>
type EditAssociationFormData = z.infer<typeof editAssociationSchema>

export const BrandProductTypeAssociationPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  // Add state for editing
  const [editingAssociation, setEditingAssociation] = useState<GroupedAssociation | null>(null)
  // Add state for selected product type in create modal
  const [selectedProductTypeId, setSelectedProductTypeId] = useState<number>(0)

  // API hooks
  const { data: associations, isLoading } = useBrandProductTypeAssociations()
  const { data: brands } = useProductBrands()
  const { data: productTypes } = useProductTypes()
  const { data: selectedProductTypeBrands } = useProductTypeBrands(selectedProductTypeId)
  const associateBrandsMutation = useAssociateBrandsWithProductType()
  const manageBrandsMutation = useManageProductTypeBrands()

  // Form setup
  const form = useForm<AssociationFormData>({
    resolver: zodResolver(associationSchema),
    defaultValues: {
      product_type: 0,
      brands: [],
    },
  })

  // Edit modal form setup
  const editForm = useForm<EditAssociationFormData>({
    resolver: zodResolver(editAssociationSchema),
    defaultValues: {
      product_type: 0,
      brands: [],
    },
  })

  // When editingAssociation changes, reset the form with its values
  React.useEffect(() => {
    if (editingAssociation) {
      editForm.reset({
        product_type: editingAssociation.product_type,
        brands: editingAssociation.brands.map(b => b.id),
      })
    }
  }, [editingAssociation, editForm])

  // Auto-populate brands when product type is selected in create modal
  React.useEffect(() => {
    if (selectedProductTypeId && selectedProductTypeBrands) {
      const existingBrandIds = selectedProductTypeBrands.map(pb => pb.brand)
      form.setValue('brands', existingBrandIds)
    }
  }, [selectedProductTypeId, selectedProductTypeBrands, form])

  // Group associations by product type
  const groupedAssociations = useMemo(() => {
    if (!associations || associations.length === 0) return []
    const map = new Map()
    associations.forEach(assoc => {
      if (!map.has(assoc.product_type)) {
        map.set(assoc.product_type, {
          id: assoc.product_type, // Use product_type as unique id for DataTable
          product_type: assoc.product_type,
          product_type_title: assoc.product_type_title,
          brands: [],
        })
      }
      map.get(assoc.product_type).brands.push({ id: assoc.brand, title: assoc.brand_title })
    })
    return Array.from(map.values())
  }, [associations])

  // Table columns for grouped view
  type GroupedAssociation = {
    id: number // Using product_type as unique identifier
    product_type: number
    product_type_title: string
    brands: { id: number; title: string }[]
  }

  type Brand = { id: number; title: string }

  const columns = [
    {
      key: 'product_type_title',
      header: 'Product Type',
      render: (_value: unknown, row: GroupedAssociation) => (
        <div className={styles.productTypeCell}>
          <Badge variant="primary">{row.product_type_title}</Badge>
        </div>
      ),
    },
    {
      key: 'brands',
      header: 'Brands',
      render: (_value: unknown, row: GroupedAssociation) => (
        <div className={styles.brandCell}>
          {(row.brands ?? [])
            .filter((brand: Brand) => typeof brand.id !== 'undefined' && brand.id !== null)
            .map((brand: Brand) => (
              <span key={brand.id} style={{ marginRight: 4 }}><Badge variant="secondary">{brand.title}</Badge></span>
            ))}
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_value: unknown, row: GroupedAssociation) => (
        <div className={styles.actionsCell}>
          <PermissionGuard permission="staff.change_brandproducttype">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingAssociation(row)}
              title="Edit association"
              className={styles.editButton}
            >
              <FiEdit />
            </Button>
          </PermissionGuard>
        </div>
      ),
    },
  ]

  // Handle form submission
  const onSubmit = async (data: AssociationFormData) => {
    try {
      await associateBrandsMutation.mutateAsync({
        productTypeId: data.product_type,
        brandIds: data.brands,
      })
      setIsCreateModalOpen(false)
      setSelectedProductTypeId(0)
      form.reset()
    } catch {
      // Error is handled by the mutation hook
    }
  }

  // Handle create modal close
  const handleCreateModalClose = () => {
    setIsCreateModalOpen(false)
    setSelectedProductTypeId(0)
    form.reset()
  }

  // handleDeleteAssociation is not used in the grouped view

  // Options for react-select
  const brandOptions = brands?.map(brand => ({
    value: brand.id,
    label: brand.title,
  })) || []

  const productTypeOptions = productTypes?.map(type => ({
    value: type.id,
    label: type.title,
  })) || []

  // Edit modal submit handler (move here for hoisting)
  const handleEditSubmit = async (data: EditAssociationFormData) => {
    if (!editingAssociation) return

    try {
      const currentBrandIds = editingAssociation.brands.map(b => b.id)

      await manageBrandsMutation.mutateAsync({
        productTypeId: data.product_type,
        currentBrandIds,
        newBrandIds: data.brands,
      })

      setEditingAssociation(null)
      editForm.reset()
    } catch {
      // Error handled by mutation
    }
  }

  // Debug logging
  console.log('Debug info:', {
    isLoading,
    associations,
    associationsLength: associations?.length,
    groupedAssociations,
    groupedLength: groupedAssociations.length,
    brands,
    productTypes
  })

  if (isLoading) {
    return <PageLoading message="Loading brand-product type associations..." />
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>Brand-Product Type Associations</h1>
          <p className={styles.subtitle}>
            Manage which brands are associated with which product types
          </p>
        </div>
        <div className={styles.actions}>
          <PermissionGuard permission="staff.add_brandproducttype">
            <Button
              variant="primary"
              onClick={() => setIsCreateModalOpen(true)}
              className={styles.createButton}
            >
              <FiPlus />
              Create Association
            </Button>
          </PermissionGuard>
        </div>
      </div>

      <Card className={styles.associationsCard}>
        <CardHeader>
          <div className={styles.cardHeader}>
            <h2>All Associations</h2>
            <div className={styles.searchContainer}>
              <div className={styles.searchBox}>
                <FiSearch className={styles.searchIcon} />
                <Input
                  type="text"
                  placeholder="Search associations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={styles.searchInput}
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardBody>
          <DataTable
            data={groupedAssociations.filter(row =>
              row.product_type_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
              row.brands.some((brand: Brand) => brand.title.toLowerCase().includes(searchTerm.toLowerCase()))
            )}
            columns={columns}
            emptyMessage="No brand-product type associations found. Create your first association!"
          />
        </CardBody>
      </Card>

      {/* Create Association Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={handleCreateModalClose}
        title="Create Brand-Product Type Association"
        size="md"
      >
        <form onSubmit={form.handleSubmit(onSubmit)} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="product_type" className={styles.label}>
              Product Type *
            </label>
            <Controller
              name="product_type"
              control={form.control}
              render={({ field }) => (
                <Select
                  {...field}
                  options={productTypeOptions}
                  placeholder="Select a product type..."
                  classNamePrefix="react-select"
                  value={productTypeOptions.find(opt => opt.value === field.value) || null}
                  onChange={opt => {
                    const newValue = opt ? opt.value : 0
                    field.onChange(newValue)
                    setSelectedProductTypeId(newValue)
                  }}
                  styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                />
              )}
            />
            {form.formState.errors.product_type && (
              <span className={styles.error}>
                {form.formState.errors.product_type.message}
              </span>
            )}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="brands" className={styles.label}>
              Brands *
            </label>
            <Controller
              name="brands"
              control={form.control}
              render={({ field }) => (
                <Select
                  {...field}
                  isMulti
                  options={brandOptions}
                  placeholder="Select brands..."
                  classNamePrefix="react-select"
                  value={brandOptions.filter(opt => field.value.includes(opt.value))}
                  onChange={opts => field.onChange(Array.isArray(opts) ? opts.map(opt => opt.value) : [])}
                  styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                />
              )}
            />
            {form.formState.errors.brands && (
              <span className={styles.error}>
                {form.formState.errors.brands.message}
              </span>
            )}
            <small className={styles.helpText}>
              {selectedProductTypeId && selectedProductTypeBrands?.length ?
                `Currently associated brands are pre-selected. Add or remove brands as needed.` :
                'Select multiple brands to associate with the chosen product type'
              }
            </small>
          </div>

          <div className={styles.formActions}>
            <Button
              type="button"
              variant="secondary"
              onClick={handleCreateModalClose}
              disabled={associateBrandsMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={associateBrandsMutation.isPending}
              className={styles.submitButton}
            >
              <FiLink />
              {associateBrandsMutation.isPending ? 'Creating...' : 'Create Association'}
            </Button>
          </div>
        </form>
      </Modal>

      {/* Edit Association Modal */}
      <Modal
        isOpen={!!editingAssociation}
        onClose={() => {
          setEditingAssociation(null)
          editForm.reset()
        }}
        title="Edit Brand-Product Type Association"
        size="md"
      >
        <form onSubmit={editForm.handleSubmit(handleEditSubmit)} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="edit_product_type" className={styles.label}>
              Product Type
            </label>
            <Controller
              name="product_type"
              control={editForm.control}
              render={({ field }) => (
                <Select
                  {...field}
                  options={productTypeOptions}
                  placeholder="Select a product type..."
                  classNamePrefix="react-select"
                  value={productTypeOptions.find(opt => opt.value === field.value) || null}
                  isDisabled
                  styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                />
              )}
            />
          </div>



          <div className={styles.formGroup}>
            <label htmlFor="edit_brands" className={styles.label}>
              Brands
            </label>
            <Controller
              name="brands"
              control={editForm.control}
              render={({ field }) => (
                <Select
                  {...field}
                  isMulti
                  options={brandOptions}
                  placeholder="Select brands (leave empty to remove all associations)..."
                  classNamePrefix="react-select"
                  value={brandOptions.filter(opt => field.value.includes(opt.value))}
                  onChange={opts => field.onChange(Array.isArray(opts) ? opts.map(opt => opt.value) : [])}
                  styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                />
              )}
            />
            {editForm.formState.errors.brands && (
              <span className={styles.error}>
                {editForm.formState.errors.brands.message}
              </span>
            )}
            <small className={styles.helpText}>
              Select brands to associate with this product type. Leave empty to remove all brand associations.
            </small>
          </div>

          <div className={styles.formActions}>
            <Button
              type="button"
              variant="secondary"
              onClick={() => {
                setEditingAssociation(null)
                editForm.reset()
              }}
              disabled={manageBrandsMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={manageBrandsMutation.isPending}
              className={styles.submitButton}
            >
              <FiEdit />
              {manageBrandsMutation.isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  )
}
