@use '../../../../scss/variables.scss' as *;
@use '../../../../scss/mixins.scss' as *;


.header {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-md;

  div {
    h2 {
      @include heading-md;
      margin: 0 0 $spacing-xs 0;
      color: $text-primary;
    }

    p {
      @include text-sm;
      margin: 0;
      color: $text-secondary;
    }
  }
}

.editButton {
  @include flex-center;
  gap: $spacing-sm;
  font-size: $font-size-sm;

  svg {
    width: 16px;
    height: 16px;
  }
}

.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.titleSlugSection {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-lg;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;

  label {
    @include text-sm;
    font-weight: 500;
    color: $text-primary;
  }
}

.textarea {
  @include input-base;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
}

.switchGroup {
  @include flex-between;
  align-items: center;
  padding: $spacing-sm 0;

  label {
    margin: 0;
  }
}

.error {
  @include text-xs;
  color: $error-color;
  margin-top: $spacing-xs;
}

.formActions {
  @include flex-end;
  gap: $spacing-md;
  padding-top: $spacing-lg;
  border-top: 1px solid $border-color;
}

.displayMode {
  .titleSlugDisplay {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
    margin-bottom: $spacing-xl;
    padding-bottom: $spacing-lg;
    border-bottom: 1px solid $border-light;
  }

  .titleBlock,
  .slugBlock {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;

    label {
      @include text-xs;
      font-weight: 600;
      color: $text-secondary;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .titleValue {
    @include heading-md;
    color: $text-primary;
    font-weight: 600;
    line-height: 1.3;
  }

  .slugValue {
    @include text-base;
    color: $text-secondary;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background-color: $background-secondary;
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius;
    border: 1px solid $border-light;
  }

  .infoGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-lg;
    margin-bottom: $spacing-lg;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .infoItem {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;

    label {
      @include text-xs;
      font-weight: 600;
      color: $text-secondary;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    span {
      @include text-sm;
      color: $text-primary;

      &.active {
        color: $success-color;
        font-weight: 500;
      }

      &.inactive {
        color: $error-color;
        font-weight: 500;
      }
    }
  }

  .descriptionSection {
    .infoItem {
      span {
        white-space: pre-wrap;
        line-height: 1.5;
      }
    }
  }
}

// React Select custom styles
:global(.react-select__control) {
  @include input-base;
  border: 1px solid $border-color !important;
  box-shadow: none !important;
  min-height: 40px;

  &:hover {
    border-color: $primary-color !important;
  }
}

:global(.react-select__control--is-focused) {
  border-color: $primary-color !important;
  box-shadow: 0 0 0 2px rgba($primary-color, 0.1) !important;
}

:global(.react-select__value-container) {
  padding: 0 $spacing-sm;
}

:global(.react-select__placeholder) {
  color: $text-placeholder;
}

:global(.react-select__single-value) {
  color: $text-primary;
}

:global(.react-select__menu) {
  border: 1px solid $border-color;
  box-shadow: $shadow-md;
  z-index: 9999;
}

:global(.react-select__option) {}

:global(.react-select__option:hover) {
  background-color: $background-hover;
}

:global(.react-select__option--is-selected) {
  background-color: $primary-color;
}

:global(.react-select__option--is-focused) {
  background-color: $primary-light;
}