// Product Images Section for managing product images
// Handles upload, update, and delete functionality with modal-based interfaces

import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiUpload, FiEdit, FiTrash2, FiImage, FiX, FiSave, FiEye } from 'react-icons/fi'
import { DndProvider, useDrag, useDrop } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import {
  useProductImages,
  useUploadProductImage,
  // useUpdateProductImageDetails,
  useDeleteProductImage,
  useReorderProductImagesDragDrop
} from '../../../../hooks/products-hooks/use-product-images'
import { Card, CardHeader, CardBody } from '../../../../components/ui/Card'
import { Button } from '../../../../components/ui/Button'
import { Input } from '../../../../components/ui/Input'
import { Modal } from '../../../../components/ui/Modal'
import { ButtonLoading, PageLoading } from '../../../../components/ui/LoadingSpinner'
import { Badge } from '../../../../components/ui/Badge'
import { DragIndicator } from '../../../../components/ui/DragIndicator'
import type { Product, ProductVariant, ProductImage } from '../../../../types/api-types'
import styles from './ProductImagesSection.module.scss'

const imageUploadSchema = z.object({
  alternative_text: z.string().min(1, 'Alt text is required'),
  image: z.instanceof(File, { message: 'Image file is required' }),
})

const imageEditSchema = z.object({
  alternative_text: z.string().min(1, 'Alt text is required'),
})

type ImageUploadFormData = z.infer<typeof imageUploadSchema>
type ImageEditFormData = z.infer<typeof imageEditSchema>

interface ProductImagesSectionProps {
  product: Product
}

const DND_ITEM_TYPE = 'IMAGE'

export const ProductImagesSection: React.FC<ProductImagesSectionProps> = ({ product }) => {
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null)
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)
  const [editingImage, setEditingImage] = useState<ProductImage | null>(null)
  const [deletingImage, setDeletingImage] = useState<ProductImage | null>(null)
  const [previewImage, setPreviewImage] = useState<ProductImage | null>(null)
  const [uploadFiles, setUploadFiles] = useState<File[]>([])

  const variants = product.variants || []
  const { data: images, isLoading: imagesLoading } = useProductImages(selectedVariant?.id || 0)
  const uploadImageMutation = useUploadProductImage()
  // const updateImageMutation = useUpdateProductImageDetails()
  // const updateImageMutation = useUpdateProductImage()
  const deleteImageMutation = useDeleteProductImage()
  const reorderImagesMutation = useReorderProductImagesDragDrop()

  const uploadForm = useForm<ImageUploadFormData>({
    resolver: zodResolver(imageUploadSchema),
    defaultValues: {
      alternative_text: '',
    },
  })

  const editForm = useForm<ImageEditFormData>({
    resolver: zodResolver(imageEditSchema),
  })

  // Set first variant as selected by default
  React.useEffect(() => {
    if (variants && variants.length > 0 && !selectedVariant) {
      setSelectedVariant(variants[0])
    }
  }, [variants, selectedVariant])

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setUploadFiles(acceptedFiles)
    if (acceptedFiles.length > 0) {
      uploadForm.setValue('image', acceptedFiles[0])
      // Generate alt text from filename
      const filename = acceptedFiles[0].name.replace(/\.[^/.]+$/, '')
      const altText = filename.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      uploadForm.setValue('alternative_text', altText)
    }
  }, [uploadForm])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    multiple: false,
    maxSize: 5 * 1024 * 1024, // 5MB
  })

  const handleUploadImage = async (data: ImageUploadFormData) => {
    if (!selectedVariant) return

    try {
      await uploadImageMutation.mutateAsync({
        image: data.image,
        alternative_text: data.alternative_text,
        product_variant: selectedVariant.id,
      })
      setIsUploadModalOpen(false)
      setUploadFiles([])
      uploadForm.reset()
    } catch (error) {
      console.error('Failed to upload image:', error)
    }
  }

  const handleEditImage = (image: ProductImage) => {
    setEditingImage(image)
    editForm.reset({
      alternative_text: image.alternative_text || '',
    })
  }

  const handleUpdateImage = async (data: ImageEditFormData) => {
    if (!editingImage) return

    try {
      await updateImageMutation.mutateAsync({
        id: editingImage.id,
        data,
      })
      setEditingImage(null)
    } catch (error) {
      console.error('Failed to update image:', error)
    }
  }

  const handleDeleteImage = async () => {
    if (!deletingImage) return

    try {
      await deleteImageMutation.mutateAsync(deletingImage.id)
      setDeletingImage(null)
    } catch (error) {
      console.error('Failed to delete image:', error)
    }
  }

  const handleImageReorder = async (draggedImageId: number, targetImageId: number) => {
    if (!images || !selectedVariant || draggedImageId === targetImageId) return

    // Create new order based on current images
    const currentImages = [...images].sort((a, b) => a.order - b.order)
    const draggedIndex = currentImages.findIndex(img => img.id === draggedImageId)
    const targetIndex = currentImages.findIndex(img => img.id === targetImageId)

    if (draggedIndex === -1 || targetIndex === -1) return

    // Reorder the array
    const reorderedImages = [...currentImages]
    const [draggedItem] = reorderedImages.splice(draggedIndex, 1)
    reorderedImages.splice(targetIndex, 0, draggedItem)

    // Create ordered IDs array
    const orderedIds = reorderedImages.map(img => img.id)

    try {
      await reorderImagesMutation.mutateAsync({
        productVariantId: selectedVariant.id,
        orderedIds
      })
    } catch (error) {
      console.error('Failed to reorder images:', error)
    }
  }

  const handleCancelEdit = () => {
    setEditingImage(null)
    editForm.reset()
  }

  // Draggable Image Card Component
  const DraggableImageCard: React.FC<{ image: ProductImage; index: number }> = ({ image, index }) => {
    const [{ isDragging }, dragRef] = useDrag({
      type: DND_ITEM_TYPE,
      item: { id: image.id, index },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    })

    const [{ isOver }, dropRef] = useDrop({
      accept: DND_ITEM_TYPE,
      drop: (item: { id: number; index: number }) => {
        if (item.id !== image.id) {
          handleImageReorder(item.id, image.id)
        }
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
    })

    const dragDropRef = (node: HTMLDivElement | null) => {
      dragRef(node)
      dropRef(node)
    }

    return (
      <div
        ref={dragDropRef}
        className={`${styles.imageCard} ${isDragging ? styles.dragging : ''} ${isOver ? styles.dropTarget : ''}`}
        style={{ opacity: isDragging ? 0.5 : 1 }}
      >
        {editingImage?.id === image.id ? (
          <form
            onSubmit={editForm.handleSubmit(handleUpdateImage)}
            className={styles.editForm}
          >
            <div className={styles.imagePreview}>
              <img
                src={`${import.meta.env.VITE_CLOUDINARY_URL}/${image.image}`}
                alt={image.alternative_text}
                className={styles.image}
              />
            </div>
            <div className={styles.formGroup}>
              <Input
                {...editForm.register('alternative_text')}
                placeholder="Alternative text"
                error={editForm.formState.errors.alternative_text?.message}
              />
            </div>
            <div className={styles.editActions}>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setEditingImage(null)}
              >
                <FiX />
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                size="sm"
                disabled={updateImageMutation.isPending}
              >
                <FiSave />
                Save
              </Button>
            </div>
          </form>
        ) : (
          <div className={styles.imageContent}>
            <div className={styles.imagePreview}>
              <img
                src={`${import.meta.env.VITE_CLOUDINARY_URL}/${image.image}`}
                alt={image.alternative_text}
                className={styles.image}
              />
              <div className={styles.imageOverlay}>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setPreviewImage(image)}
                  className={styles.overlayButton}
                >
                  <FiEye />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEditImage(image)}
                  className={styles.overlayButton}
                >
                  <FiEdit />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setDeletingImage(image)}
                  className={`${styles.overlayButton} ${styles.deleteButton}`}
                >
                  <FiTrash2 />
                </Button>
              </div>
              <div className={styles.dragIndicatorOverlay}>
                <DragIndicator variant="compact" tooltip="Drag to reorder images" />
              </div>
            </div>
            <div className={styles.imageInfo}>
              <p className={styles.altText}>
                {image.alternative_text || 'No alt text'}
              </p>
              <span className={styles.imageOrder}>
                Order: {image.order}
              </span>
            </div>
          </div>
        )}
      </div>
    )
  }

  if (variants.length === 0) {
    return (
      <Card>
        <CardHeader>
          <h2>Product Images</h2>
          <p>Upload and manage product images for variants</p>
        </CardHeader>
        <CardBody>
          <div className={styles.emptyState}>
            <FiImage size={48} />
            <h3>No Variants Available</h3>
            <p>You need to create product variants before you can upload images.</p>
          </div>
        </CardBody>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className={styles.header}>
            <div>
              <h2>Product Images</h2>
              <p>Upload and manage product images for variants</p>
            </div>
            <Button
              variant="primary"
              size="sm"
              onClick={() => setIsUploadModalOpen(true)}
              disabled={!selectedVariant}
            >
              <FiUpload />
              Upload Image
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          {/* Variant Selector */}
          <div className={styles.variantSelector}>
            <label>Select Variant:</label>
            <div className={styles.variantTabs}>
              {variants.map((variant) => (
                <button
                  key={variant.id}
                  className={`${styles.variantTab} ${selectedVariant?.id === variant.id ? styles.active : ''
                    }`}
                  onClick={() => setSelectedVariant(variant)}
                >
                  <span className={styles.variantSku}>{variant.price_label_title}</span>
                  <Badge variant={variant.is_active ? 'success' : 'error'} size="sm">
                    {variant.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </button>
              ))}
            </div>
          </div>

          {/* Images Gallery */}
          {selectedVariant && (
            <div className={styles.imagesSection}>
              <div className={styles.sectionHeader}>
                <h3>Images for {selectedVariant.sku}</h3>
                <span className={styles.imageCount}>
                  {images?.length || 0} image{(images?.length || 0) !== 1 ? 's' : ''}
                </span>
              </div>

              {imagesLoading ? (
                <div className={styles.loadingState}>
                  <PageLoading message="Loading images..." />
                </div>
              ) : !images || images.length === 0 ? (
                <div className={styles.emptyImages}>
                  <FiImage size={48} />
                  <h4>No Images</h4>
                  <p>No images have been uploaded for this variant yet.</p>
                  <Button
                    variant="outline"
                    onClick={() => setIsUploadModalOpen(true)}
                  >
                    <FiUpload />
                    Upload First Image
                  </Button>
                </div>
              ) : (
                <DndProvider backend={HTML5Backend}>
                  <div className={styles.imageGrid}>
                    {images.map((image, index) => (
                      <DraggableImageCard key={image.id} image={image} index={index} />
                    ))}
                  </div>
                </DndProvider>
              )}
            </div>
          )}
        </CardBody>
      </Card >

      {/* Upload Image Modal */}
      < Modal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        title="Upload Image"
        size="md"
      >
        <form onSubmit={uploadForm.handleSubmit(handleUploadImage)} className={styles.modalForm}>
          <div className={styles.dropzoneContainer}>
            <div
              {...getRootProps()}
              className={`${styles.dropzone} ${isDragActive ? styles.dragActive : ''}`}
            >
              <input {...getInputProps()} />
              {uploadFiles.length > 0 ? (
                <div className={styles.filePreview}>
                  <img
                    src={URL.createObjectURL(uploadFiles[0])}
                    alt="Preview"
                    className={styles.previewImage}
                  />
                  <p>{uploadFiles[0].name}</p>
                </div>
              ) : (
                <div className={styles.dropzoneContent}>
                  <FiUpload size={48} />
                  <h4>Drop image here or click to browse</h4>
                  <p>Supports: JPEG, PNG, GIF, WebP (max 5MB)</p>
                </div>
              )}
            </div>
          </div>

          <div className={styles.formGroup}>
            <label>Alt Text *</label>
            <Input
              {...uploadForm.register('alternative_text')}
              error={uploadForm.formState.errors.alternative_text?.message}
              placeholder="Enter descriptive alt text"
            />
          </div>

          <div className={styles.modalActions}>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsUploadModalOpen(false)}
              disabled={uploadImageMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={uploadImageMutation.isPending || uploadFiles.length === 0}
            >
              <ButtonLoading
                isLoading={uploadImageMutation.isPending}
                loadingText="Uploading..."
              >
                <FiUpload />
                Upload Image
              </ButtonLoading>
            </Button>
          </div>
        </form>
      </Modal >

      {/* Delete Confirmation Modal */}
      < Modal
        isOpen={!!deletingImage}
        onClose={() => setDeletingImage(null)}
        title="Delete Image"
        size="sm"
      >
        <div className={styles.deleteModal}>
          {deletingImage && (
            <>
              <div className={styles.deleteImagePreview}>
                <img
                  src={`${import.meta.env.VITE_CLOUDINARY_URL}/${deletingImage.image}`}
                  alt={deletingImage.alternative_text}
                  className={styles.deleteImage}
                />
              </div>
              <p>
                Are you sure you want to delete this image? This action cannot be undone.
              </p>
              <div className={styles.modalActions}>
                <Button
                  variant="outline"
                  onClick={() => setDeletingImage(null)}
                  disabled={deleteImageMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  variant="danger"
                  onClick={handleDeleteImage}
                  disabled={deleteImageMutation.isPending}
                >
                  <ButtonLoading
                    isLoading={deleteImageMutation.isPending}
                    loadingText="Deleting..."
                  >
                    <FiTrash2 />
                    Delete
                  </ButtonLoading>
                </Button>
              </div>
            </>
          )}
        </div>
      </Modal >

      {/* Image Preview Modal */}
      <Modal
        isOpen={!!previewImage}
        onClose={() => setPreviewImage(null)}
        title="Image Preview"
        size="lg"
      >
        {previewImage && (
          <div className={styles.previewModal}>
            <div className={styles.previewImageContainer}>
              <img
                src={`${import.meta.env.VITE_CLOUDINARY_URL}/${previewImage.image}`}
                alt={previewImage.alternative_text}
                className={styles.previewImageLarge}
              />
            </div>
            <div className={styles.previewInfo}>
              <h4>Image Details</h4>
              <p><strong>Alt Text:</strong> {previewImage.alternative_text || 'No alt text'}</p>
              <p><strong>Order:</strong> {previewImage.order}</p>
            </div>
          </div>
        )}
      </Modal>
    </>
  )
}

export default ProductImagesSection
