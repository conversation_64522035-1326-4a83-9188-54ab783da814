// Product Details Section for editing basic product information
// Handles title, slug, description, category, brand, product_type, is_active, is_digital

import React, { useState } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiSave, FiX, FiEdit } from 'react-icons/fi'
import Select from 'react-select'
import {useUpdateProductDetails} from '../../../../hooks/use-products'
import {useProductCategories} from '../../../../hooks/products-hooks/use-categories'
import {useProductBrands} from '../../../../hooks/products-hooks/use-brands'
import {useProductTypes} from '../../../../hooks/products-hooks/use-product-types'
import { Card, CardHeader, CardBody } from '../../../../components/ui/Card'
import { Button } from '../../../../components/ui/Button'
import { Input } from '../../../../components/ui/Input'
import { Switch } from '../../../../components/ui/Switch'
import { ButtonLoading } from '../../../../components/ui/LoadingSpinner'
import type { Product } from '../../../../types/api-types'
import styles from './ProductDetailsSection.module.scss'

const productDetailsSchema = z.object({
  title: z.string().min(1, 'Product title is required'),
  slug: z.string().min(1, 'Product slug is required'),
  description: z.string().min(1, 'Product description is required'),
  category: z.number().min(1, 'Category is required'),
  brand: z.number().min(1, 'Brand is required'),
  product_type: z.number().min(1, 'Product type is required'),
  is_active: z.boolean(),
  is_digital: z.boolean(),
})

type ProductDetailsFormData = z.infer<typeof productDetailsSchema>

interface ProductDetailsSectionProps {
  product: Product
}

export const ProductDetailsSection: React.FC<ProductDetailsSectionProps> = ({ product }) => {
  const [isEditing, setIsEditing] = useState(true) // Default to edit mode

  const { data: categories } = useProductCategories()
  const { data: brands } = useProductBrands()
  const { data: productTypes } = useProductTypes()
  const updateProductMutation = useUpdateProductDetails(product.id)

  const form = useForm<ProductDetailsFormData>({
    resolver: zodResolver(productDetailsSchema),
    defaultValues: {
      title: product.title || '',
      slug: product.slug || '',
      description: product.description || '',
      category: product.category || 0,
      brand: product.brand || 0,
      product_type: product.product_type || 0,
      is_active: product.is_active || false,
      is_digital: product.is_digital || false,
    },
  })

  // Auto-generate slug from title
  const handleTitleChange = (title: string) => {
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
    form.setValue('slug', slug)
  }

  const handleSave = async (data: ProductDetailsFormData) => {
    try {
      await updateProductMutation.mutateAsync(data)
      setIsEditing(false) // Switch to view mode after successful update
    } catch (error) {
      console.error('Failed to update product:', error)
    }
  }

  const handleCancel = () => {
    form.reset()
    setIsEditing(false)
  }

  const handleEdit = () => {
    setIsEditing(true)
  }

  // Options for react-select
  const categoryOptions = categories?.map(cat => ({
    value: cat.id,
    label: cat.title,
  })) || []

  const brandOptions = brands?.map(brand => ({
    value: brand.id,
    label: brand.title,
  })) || []

  const productTypeOptions = productTypes?.map(type => ({
    value: type.id,
    label: type.title,
  })) || []

  const isLoading = updateProductMutation.isPending

  return (
    <Card>
      <CardHeader>
        <div className={styles.header}>
          <div>
            <h2>Product Details</h2>
            <p>Basic product information and settings</p>
          </div>
          {!isEditing && (
            <Button
              variant="outline"
              onClick={handleEdit}
              className={styles.editButton}
            >
              <FiEdit />
              Edit Details
            </Button>
          )}
        </div>
      </CardHeader>
      <CardBody>
        {isEditing ? (
          <form onSubmit={form.handleSubmit(handleSave)} className={styles.form}>
            <div className={styles.titleSlugSection}>
              <div className={styles.formGroup}>
                <label htmlFor="title">Product Title *</label>
                <Input
                  id="title"
                  {...form.register('title')}
                  onChange={(e) => {
                    form.setValue('title', e.target.value)
                    handleTitleChange(e.target.value)
                  }}
                  error={form.formState.errors.title?.message}
                  placeholder="Enter product title"
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="slug">Product Slug *</label>
                <Input
                  id="slug"
                  {...form.register('slug')}
                  error={form.formState.errors.slug?.message}
                  placeholder="product-slug"
                  helperText="URL-friendly version of the product title"
                />
              </div>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="description">Description *</label>
              <textarea
                id="description"
                {...form.register('description')}
                className={styles.textarea}
                rows={4}
                placeholder="Enter product description"
              />
              {form.formState.errors.description && (
                <span className={styles.error}>
                  {form.formState.errors.description.message}
                </span>
              )}
            </div>

            <div className={styles.formGrid}>
              <div className={styles.formGroup}>
                <label htmlFor="category">Category *</label>
                <Controller
                  name="category"
                  control={form.control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={categoryOptions}
                      placeholder="Select category..."
                      classNamePrefix="react-select"
                      value={categoryOptions.find(opt => opt.value === field.value) || null}
                      onChange={opt => field.onChange(opt ? opt.value : 0)}
                      styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                    />
                  )}
                />
                {form.formState.errors.category && (
                  <span className={styles.error}>
                    {form.formState.errors.category.message}
                  </span>
                )}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="brand">Brand *</label>
                <Controller
                  name="brand"
                  control={form.control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={brandOptions}
                      placeholder="Select brand..."
                      classNamePrefix="react-select"
                      value={brandOptions.find(opt => opt.value === field.value) || null}
                      onChange={opt => field.onChange(opt ? opt.value : 0)}
                      styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                    />
                  )}
                />
                {form.formState.errors.brand && (
                  <span className={styles.error}>
                    {form.formState.errors.brand.message}
                  </span>
                )}
              </div>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="product_type">Product Type *</label>
              <Controller
                name="product_type"
                control={form.control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={productTypeOptions}
                    placeholder="Select product type..."
                    classNamePrefix="react-select"
                    value={productTypeOptions.find(opt => opt.value === field.value) || null}
                    onChange={opt => field.onChange(opt ? opt.value : 0)}
                    styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                  />
                )}
              />
              {form.formState.errors.product_type && (
                <span className={styles.error}>
                  {form.formState.errors.product_type.message}
                </span>
              )}
            </div>

            <div className={styles.formGrid}>
              <div className={styles.formGroup}>
                <div className={styles.switchGroup}>
                  <label>Is Active</label>
                  <Controller
                    name="is_active"
                    control={form.control}
                    render={({ field }) => (
                      <Switch
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                      />
                    )}
                  />
                </div>
              </div>

              <div className={styles.formGroup}>
                <div className={styles.switchGroup}>
                  <label>Is Digital</label>
                  <Controller
                    name="is_digital"
                    control={form.control}
                    render={({ field }) => (
                      <Switch
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                      />
                    )}
                  />
                </div>
              </div>
            </div>

            <div className={styles.formActions}>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isLoading}
              >
                <FiX />
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={isLoading}
              >
                <ButtonLoading
                  isLoading={isLoading}
                  loadingText="Updating..."
                >
                  <FiSave />
                  Update Details
                </ButtonLoading>
              </Button>
            </div>
          </form>
        ) : (
          <div className={styles.displayMode}>
            {/* Title and Slug in separate blocks */}
            <div className={styles.titleSlugDisplay}>
              <div className={styles.titleBlock}>
                <label>Product Title</label>
                <span className={styles.titleValue}>{product.title}</span>
              </div>
              <div className={styles.slugBlock}>
                <label>Product Slug</label>
                <span className={styles.slugValue}>{product.slug}</span>
              </div>
            </div>

            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <label>Category</label>
                <span>{categories?.find(cat => cat.id === product.category)?.title || 'N/A'}</span>
              </div>
              <div className={styles.infoItem}>
                <label>Brand</label>
                <span>{brands?.find(brand => brand.id === product.brand)?.title || 'N/A'}</span>
              </div>
              <div className={styles.infoItem}>
                <label>Product Type</label>
                <span>{productTypes?.find(type => type.id === product.product_type)?.title || 'N/A'}</span>
              </div>
              <div className={styles.infoItem}>
                <label>Status</label>
                <span className={product.is_active ? styles.active : styles.inactive}>
                  {product.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
              {/* <div className={styles.infoItem}>
                <label>Product Type</label>
                <span className={product.is_digital ? styles.active : styles.inactive}>
                  {product.is_digital ? 'Digital' : 'Physical'}
                </span>
              </div> */}
            </div>
            <div className={styles.descriptionSection}>
              <div className={styles.infoItem}>
                <label>Description</label>
                <span>{product.description}</span>
              </div>
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  )
}

export default ProductDetailsSection
