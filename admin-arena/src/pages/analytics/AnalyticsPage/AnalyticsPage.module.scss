@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  padding: $spacing-6;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  @include flex-between;
  margin-bottom: $spacing-6;
  gap: $spacing-4;
  
  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.titleSection {
  flex: 1;
}

.title {
  margin: 0 0 $spacing-2 0;
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
}

.subtitle {
  margin: 0;
  color: $gray-600;
  font-size: $font-size-base;
}

.periodSelector {
  @include flex-start;
  gap: $spacing-2;
  
  @include mobile-only {
    width: 100%;
    overflow-x: auto;
    padding-bottom: $spacing-2;
  }
}

// Metrics Grid
.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $spacing-4;
  margin-bottom: $spacing-6;
}

.metricCard {
  padding: $spacing-5;
}

.metricContent {
  @include flex-start;
  gap: $spacing-4;
}

.metricIcon {
  @include flex-center;
  width: 48px;
  height: 48px;
  background-color: $primary-100;
  border-radius: $border-radius-lg;
  color: $primary-600;
  flex-shrink: 0;
  
  svg {
    width: 24px;
    height: 24px;
  }
}

.metricInfo {
  flex: 1;
}

.metricValue {
  margin: 0 0 $spacing-1 0;
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
  line-height: 1.2;
}

.metricLabel {
  margin: 0 0 $spacing-2 0;
  font-size: $font-size-sm;
  color: $gray-600;
  font-weight: $font-weight-medium;
}

.metricTrend {
  @include flex-start;
  gap: $spacing-2;
}

// Charts Grid
.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: $spacing-4;
  margin-bottom: $spacing-6;
  
  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.chartCard {
  min-height: 300px;
}

.chartPlaceholder {
  @include flex-column-center;
  min-height: 200px;
  background-color: $gray-50;
  border-radius: $border-radius;
  border: 2px dashed $gray-300;
  text-align: center;
  gap: $spacing-2;
  
  p {
    margin: 0;
    color: $gray-600;
    font-size: $font-size-sm;
  }
}

// Additional Sections
.additionalSections {
  display: grid;
  gap: $spacing-4;
}

.summaryCard {
  width: 100%;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-4;
  
  @include mobile-only {
    grid-template-columns: repeat(2, 1fr);
  }
}

.summaryItem {
  @include flex-column;
  gap: $spacing-2;
  padding: $spacing-4;
  background-color: $gray-50;
  border-radius: $border-radius;
  text-align: center;
}

.summaryLabel {
  font-size: $font-size-sm;
  color: $gray-600;
  font-weight: $font-weight-medium;
}

.summaryValue {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $gray-900;
}

.error {
  padding: $spacing-6;
  text-align: center;
  
  h2 {
    margin: 0 0 $spacing-3 0;
    color: $red-600;
  }
  
  p {
    margin: 0 0 $spacing-4 0;
    color: $gray-600;
  }
}

// Responsive adjustments
@include mobile-only {
  .container {
    padding: $spacing-4;
  }
  
  .title {
    font-size: $font-size-xl;
  }
  
  .metricsGrid {
    grid-template-columns: 1fr;
  }
  
  .metricContent {
    gap: $spacing-3;
  }
  
  .metricIcon {
    width: 40px;
    height: 40px;
    
    svg {
      width: 20px;
      height: 20px;
    }
  }
  
  .metricValue {
    font-size: $font-size-xl;
  }
  
  .summaryGrid {
    grid-template-columns: 1fr;
  }
}
