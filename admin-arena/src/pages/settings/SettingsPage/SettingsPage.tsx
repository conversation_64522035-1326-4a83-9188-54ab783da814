// Settings page component
// User profile and system settings management

import React, { useState } from 'react'
import { <PERSON><PERSON>ser, <PERSON>Lock, FiSettings, FiBell, FiSave } from 'react-icons/fi'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useAuth, useUpdateProfile, useChangePassword } from '../../../hooks/use-auth'
import { Card } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Input } from '../../../components/ui/Input'
import { PageLoading, ButtonLoading } from '../../../components/ui/LoadingSpinner'
import styles from './SettingsPage.module.scss'

const profileSchema = z.object({
  email: z.string().email('Invalid email address'),
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
})

const passwordSchema = z.object({
  current_password: z.string().min(1, 'Current password is required'),
  new_password: z.string().min(8, 'Password must be at least 8 characters'),
  confirm_password: z.string().min(1, 'Please confirm your password'),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"],
})

type ProfileFormData = z.infer<typeof profileSchema>
type PasswordFormData = z.infer<typeof passwordSchema>

export const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'profile' | 'password' | 'preferences'>('profile')
  const { user, isLoading: authLoading } = useAuth()
  const updateProfileMutation = useUpdateProfile()
  const changePasswordMutation = useChangePassword()

  const profileForm = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      email: user?.email || '',
      first_name: user?.staff_profile?.full_name?.split(' ')[0] || '',
      last_name: user?.staff_profile?.full_name?.split(' ').slice(1).join(' ') || '',
    },
  })

  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      current_password: '',
      new_password: '',
      confirm_password: '',
    },
  })

  const tabs = [
    { id: 'profile', label: 'Profile', icon: FiUser },
    { id: 'password', label: 'Password', icon: FiLock },
    { id: 'preferences', label: 'Preferences', icon: FiSettings },
  ] as const

  const handleProfileSubmit = async (data: ProfileFormData) => {
    try {
      await updateProfileMutation.mutateAsync({
        email: data.email,
        // Note: Backend expects full_name, so we combine first and last
        staff_profile: {
          ...user?.staff_profile,
          full_name: `${data.first_name} ${data.last_name}`,
        },
      })
    } catch (error) {
      console.error('Profile update failed:', error)
    }
  }

  const handlePasswordSubmit = async (data: PasswordFormData) => {
    try {
      await changePasswordMutation.mutateAsync(data)
      passwordForm.reset()
    } catch (error) {
      console.error('Password change failed:', error)
    }
  }

  if (authLoading) {
    return <PageLoading message="Loading settings..." />
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>Settings</h1>
        <p className={styles.subtitle}>
          Manage your account settings and preferences
        </p>
      </div>

      <div className={styles.content}>
        <div className={styles.sidebar}>
          <nav className={styles.tabNav}>
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`${styles.tabButton} ${
                  activeTab === tab.id ? styles.active : ''
                }`}
                onClick={() => setActiveTab(tab.id)}
              >
                <tab.icon className={styles.tabIcon} />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className={styles.main}>
          {activeTab === 'profile' && (
            <Card className={styles.settingsCard}>
              <Card.Header>
                <h2>Profile Information</h2>
                <p>Update your personal information and contact details.</p>
              </Card.Header>
              
              <Card.Body>
                <form onSubmit={profileForm.handleSubmit(handleProfileSubmit)}>
                  <div className={styles.formGrid}>
                    <div className={styles.formGroup}>
                      <label htmlFor="email">Email Address</label>
                      <Input
                        id="email"
                        type="email"
                        {...profileForm.register('email')}
                        error={profileForm.formState.errors.email?.message}
                      />
                    </div>

                    <div className={styles.formGroup}>
                      <label htmlFor="first_name">First Name</label>
                      <Input
                        id="first_name"
                        type="text"
                        {...profileForm.register('first_name')}
                        error={profileForm.formState.errors.first_name?.message}
                      />
                    </div>

                    <div className={styles.formGroup}>
                      <label htmlFor="last_name">Last Name</label>
                      <Input
                        id="last_name"
                        type="text"
                        {...profileForm.register('last_name')}
                        error={profileForm.formState.errors.last_name?.message}
                      />
                    </div>

                    {user?.staff_profile && (
                      <>
                        <div className={styles.formGroup}>
                          <label>Department</label>
                          <Input
                            type="text"
                            value={user.staff_profile.department}
                            disabled
                          />
                        </div>

                        <div className={styles.formGroup}>
                          <label>Position</label>
                          <Input
                            type="text"
                            value={user.staff_profile.position_title}
                            disabled
                          />
                        </div>

                        <div className={styles.formGroup}>
                          <label>Employee ID</label>
                          <Input
                            type="text"
                            value={user.staff_profile.employee_id}
                            disabled
                          />
                        </div>
                      </>
                    )}
                  </div>

                  <div className={styles.formActions}>
                    <Button
                      type="submit"
                      variant="primary"
                      disabled={updateProfileMutation.isPending}
                    >
                      <ButtonLoading
                        isLoading={updateProfileMutation.isPending}
                        loadingText="Saving..."
                      >
                        <FiSave />
                        Save Changes
                      </ButtonLoading>
                    </Button>
                  </div>
                </form>
              </Card.Body>
            </Card>
          )}

          {activeTab === 'password' && (
            <Card className={styles.settingsCard}>
              <Card.Header>
                <h2>Change Password</h2>
                <p>Update your password to keep your account secure.</p>
              </Card.Header>
              
              <Card.Body>
                <form onSubmit={passwordForm.handleSubmit(handlePasswordSubmit)}>
                  <div className={styles.formGrid}>
                    <div className={styles.formGroup}>
                      <label htmlFor="current_password">Current Password</label>
                      <Input
                        id="current_password"
                        type="password"
                        {...passwordForm.register('current_password')}
                        error={passwordForm.formState.errors.current_password?.message}
                      />
                    </div>

                    <div className={styles.formGroup}>
                      <label htmlFor="new_password">New Password</label>
                      <Input
                        id="new_password"
                        type="password"
                        {...passwordForm.register('new_password')}
                        error={passwordForm.formState.errors.new_password?.message}
                      />
                    </div>

                    <div className={styles.formGroup}>
                      <label htmlFor="confirm_password">Confirm New Password</label>
                      <Input
                        id="confirm_password"
                        type="password"
                        {...passwordForm.register('confirm_password')}
                        error={passwordForm.formState.errors.confirm_password?.message}
                      />
                    </div>
                  </div>

                  <div className={styles.formActions}>
                    <Button
                      type="submit"
                      variant="primary"
                      disabled={changePasswordMutation.isPending}
                    >
                      <ButtonLoading
                        isLoading={changePasswordMutation.isPending}
                        loadingText="Updating..."
                      >
                        <FiLock />
                        Update Password
                      </ButtonLoading>
                    </Button>
                  </div>
                </form>
              </Card.Body>
            </Card>
          )}

          {activeTab === 'preferences' && (
            <Card className={styles.settingsCard}>
              <Card.Header>
                <h2>Preferences</h2>
                <p>Customize your experience and notification settings.</p>
              </Card.Header>
              
              <Card.Body>
                <div className={styles.preferenceSection}>
                  <h3>Notifications</h3>
                  <div className={styles.preferenceGrid}>
                    <div className={styles.preferenceItem}>
                      <div className={styles.preferenceInfo}>
                        <h4>Email Notifications</h4>
                        <p>Receive email notifications for important updates</p>
                      </div>
                      <input type="checkbox" defaultChecked />
                    </div>
                    
                    <div className={styles.preferenceItem}>
                      <div className={styles.preferenceInfo}>
                        <h4>Order Updates</h4>
                        <p>Get notified when orders are assigned to you</p>
                      </div>
                      <input type="checkbox" defaultChecked />
                    </div>
                    
                    <div className={styles.preferenceItem}>
                      <div className={styles.preferenceInfo}>
                        <h4>System Alerts</h4>
                        <p>Receive alerts about system maintenance and updates</p>
                      </div>
                      <input type="checkbox" />
                    </div>
                  </div>
                </div>

                <div className={styles.formActions}>
                  <Button variant="primary">
                    <FiSave />
                    Save Preferences
                  </Button>
                </div>
              </Card.Body>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
