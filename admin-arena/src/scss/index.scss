@use './variables' as *;
@use './resets';
@use './mixins' as *;

// Import Inter font for admin interface
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');


a {
  text-decoration: none;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  // background-color: aqua;
}

html {
  font-family: $primary-font-family;
}

button {
  cursor: pointer;
  border: none;
  letter-spacing: .4px;
  border-radius: 2px;
  font-size: 16px;
}

.loading_svg {
  margin: 2px 10px;
  width: 20px;
  height: 20px;
}

.logo_header {
  display: flex;
  justify-content: center;
  background-color: $primary-dark;
  padding: 10px 0;
}

.title {
  font-size: 25px;
  font-weight: bold;
  text-align: center;
  margin: 1rem 0;
  color: $primary-dark-blue;
}

.form {
  .form_group {
    display: flex;
    flex-direction: column;
    margin: 15px 0;
    row-gap: 4px;

    .form_label {
      font-weight: bold;
      color: $primary-dark-blue;
    }

    .form_input {
      width: 100%;
      border: .1px solid $primary-dark-text-color;
      border-radius: 3px;
      padding: 5px 5px;
      font-size: 16.5px;

      &:focus {
        outline: 2px solid $lighten-blue;
        border: none;
      }
    }

    .form_error {
      color: $error-red;
      text-align: center
    }
  }
}

.empty_btn {
  @include btn($lighten-blue, #fff);
  // margin: 0 auto 0 auto;
  padding: .36rem 1.2rem;
  border: 1px solid $lighten-blue;
  letter-spacing: .7px;
  transition: all 0.2s ease;

  &:hover {
    border: 1px solid $primary-blue;
    color: $primary-blue;
  }
}

.success_message {
  margin: 1rem 0;
  // padding: 0 0 1rem 0;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}

.btn_container {
  // background-color: rgb(132, 189, 189);
  display: flex;
  flex-direction: row;
  justify-content: center;
  // align-items: center;
  column-gap: 1rem;
}


.password__container {
  position: relative;
  display: flex;
  align-items: center;

  // input {
  //   width: 100%;
  // }

  span {
    position: absolute;
    right: 7px;
    cursor: pointer;

    i {
      font-size: 18px;
      color: $primary-dark-text-color;
    }
  }
}

// Admin Arena specific global styles
// Override base styles for admin interface

// Enhanced body styles for admin
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: $gray-50;
  color: $gray-900;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Enhanced typography
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Inter', sans-serif;
  font-weight: $font-weight-semibold;
  line-height: $line-height-tight;
  color: $gray-900;
}

// Enhanced focus styles for accessibility
:focus-visible {
  outline: 2px solid $primary-500;
  outline-offset: 2px;
  border-radius: $border-radius-sm;
}

// Utility classes for admin interface
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.truncate {
  @include truncate;
}

.flex-center {
  @include flex-center;
}

.flex-between {
  @include flex-between;
}

// Enhanced scrollbar styles
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: $gray-100;
}

::-webkit-scrollbar-thumb {
  background: $gray-300;
  border-radius: $border-radius;

  &:hover {
    background: $gray-400;
  }
}

// Loading animation
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

// Print styles
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  body {
    background: white;
    color: black;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}


.form__help_text {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
}