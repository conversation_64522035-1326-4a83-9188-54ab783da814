# State Management Technical Plan

## Overview

This document outlines the state management strategy using Zustand for client-side state, following the patterns established in the react-ts-client application, with TanStack Query handling server state.

## Package Versions

```json
{
  "zustand": "^5.0.6",
  "@tanstack/react-query": "^5.35.1",
  "@tanstack/react-query-persist-client": "^5.79.0",
  "@tanstack/query-sync-storage-persister": "^5.79.0"
}
```

## State Architecture

### 1. State Separation Strategy

```typescript
// State Management Architecture
interface StateArchitecture {
  // Server State (TanStack Query)
  serverState: {
    orders: 'Cached API responses, mutations, optimistic updates';
    products: 'Product catalog data, search results';
    customers: 'Customer information, analytics';
    staff: 'Staff profiles, roles, permissions';
    analytics: 'Dashboard metrics, reports';
  };
  
  // Client State (Zustand)
  clientState: {
    auth: 'Authentication status, user info, permissions';
    ui: 'Sidebar state, modals, notifications';
    filters: 'Search filters, pagination, sorting';
    selections: 'Selected items, bulk operations';
    preferences: 'User preferences, settings';
  };
}
```

## Zustand Store Implementation

### 1. Authentication Store (Enhanced from react-ts-client)

```typescript
// src/stores/auth-store.ts
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { AuthService } from '../services/auth-service';
import { StaffUser } from '../types/api-types';

interface LoginCredentials {
  email: string;
  password: string;
}

interface AuthState {
  // State
  user: StaffUser | null;
  permissions: string[];
  groups: string[];
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  checkPermission: (permission: string) => boolean;
  hasGroup: (group: string) => boolean;
  clearError: () => void;
  setUser: (user: StaffUser) => void;
  setPermissions: (permissions: string[]) => void;
  setGroups: (groups: string[]) => void;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        permissions: [],
        groups: [],
        isAuthenticated: false,
        isLoading: false,
        error: null,
        
        // Actions
        login: async (credentials) => {
          set({ isLoading: true, error: null });
          try {
            const response = await AuthService.login(credentials);
            const userResponse = await AuthService.getCurrentUser();
            
            set({
              user: response.user,
              permissions: userResponse.permissions,
              groups: userResponse.groups,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Login failed';
            set({
              error: errorMessage,
              isLoading: false,
              isAuthenticated: false,
              user: null,
              permissions: [],
              groups: []
            });
            throw error;
          }
        },
        
        logout: async () => {
          set({ isLoading: true });
          try {
            await AuthService.logout();
          } finally {
            set({
              user: null,
              permissions: [],
              groups: [],
              isAuthenticated: false,
              isLoading: false,
              error: null
            });
          }
        },
        
        getCurrentUser: async () => {
          if (!AuthService.isAuthenticated()) {
            set({ isAuthenticated: false, user: null, permissions: [], groups: [] });
            return;
          }
          
          set({ isLoading: true });
          try {
            const response = await AuthService.getCurrentUser();
            set({
              user: response.user,
              permissions: response.permissions,
              groups: response.groups,
              isAuthenticated: true,
              isLoading: false
            });
          } catch (error) {
            set({
              user: null,
              permissions: [],
              groups: [],
              isAuthenticated: false,
              isLoading: false
            });
          }
        },
        
        checkPermission: (permission: string) => {
          const { permissions, user } = get();
          return user?.is_superuser || permissions.includes(permission);
        },
        
        hasGroup: (group: string) => {
          const { groups, user } = get();
          return user?.is_superuser || groups.includes(group);
        },
        
        clearError: () => set({ error: null }),
        
        setUser: (user: StaffUser) => set({ user }),
        
        setPermissions: (permissions: string[]) => set({ permissions }),
        
        setGroups: (groups: string[]) => set({ groups })
      }),
      {
        name: 'admin-auth-store',
        partialize: (state) => ({
          isAuthenticated: state.isAuthenticated,
          user: state.user,
          permissions: state.permissions,
          groups: state.groups
        })
      }
    ),
    { name: 'AuthStore' }
  )
);
```

### 2. UI State Store

```typescript
// src/stores/ui-store.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

interface Modal {
  id: string;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closable?: boolean;
}

interface UIState {
  // Sidebar
  sidebarCollapsed: boolean;
  sidebarMobileOpen: boolean;
  
  // Notifications
  notifications: Notification[];
  
  // Modals
  modals: Modal[];
  
  // Loading states
  globalLoading: boolean;
  
  // Theme
  theme: 'light' | 'dark';
  
  // Actions
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  toggleMobileSidebar: () => void;
  
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  openModal: (modal: Omit<Modal, 'id'>) => void;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
  
  setGlobalLoading: (loading: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
}

export const useUIStore = create<UIState>()(
  devtools(
    (set, get) => ({
      // Initial state
      sidebarCollapsed: false,
      sidebarMobileOpen: false,
      notifications: [],
      modals: [],
      globalLoading: false,
      theme: 'light',
      
      // Sidebar actions
      toggleSidebar: () => set((state) => ({ 
        sidebarCollapsed: !state.sidebarCollapsed 
      })),
      
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      
      toggleMobileSidebar: () => set((state) => ({ 
        sidebarMobileOpen: !state.sidebarMobileOpen 
      })),
      
      // Notification actions
      addNotification: (notification) => {
        const id = Math.random().toString(36).substr(2, 9);
        const newNotification = { ...notification, id };
        
        set((state) => ({
          notifications: [...state.notifications, newNotification]
        }));
        
        // Auto-remove notification after duration
        if (notification.duration !== 0) {
          setTimeout(() => {
            get().removeNotification(id);
          }, notification.duration || 5000);
        }
      },
      
      removeNotification: (id) => set((state) => ({
        notifications: state.notifications.filter(n => n.id !== id)
      })),
      
      clearNotifications: () => set({ notifications: [] }),
      
      // Modal actions
      openModal: (modal) => {
        const id = Math.random().toString(36).substr(2, 9);
        const newModal = { ...modal, id };
        
        set((state) => ({
          modals: [...state.modals, newModal]
        }));
      },
      
      closeModal: (id) => set((state) => ({
        modals: state.modals.filter(m => m.id !== id)
      })),
      
      closeAllModals: () => set({ modals: [] }),
      
      // Global actions
      setGlobalLoading: (loading) => set({ globalLoading: loading }),
      
      setTheme: (theme) => set({ theme })
    }),
    { name: 'UIStore' }
  )
);
```

### 3. Filter Store (Based on react-ts-client pattern)

```typescript
// src/stores/filter-store.ts
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface OrderFilters {
  status?: string;
  assigned_to?: number;
  customer?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
  page: number;
  page_size: number;
  ordering?: string;
}

interface ProductFilters {
  category?: number;
  brand?: number;
  is_active?: boolean;
  search?: string;
  price_min?: number;
  price_max?: number;
  page: number;
  page_size: number;
  ordering?: string;
}

interface CustomerFilters {
  is_active?: boolean;
  search?: string;
  date_joined_from?: string;
  date_joined_to?: string;
  page: number;
  page_size: number;
  ordering?: string;
}

interface SavedFilter {
  id: string;
  name: string;
  type: 'orders' | 'products' | 'customers';
  filters: Record<string, any>;
  created_at: string;
}

interface FilterState {
  // Current filters
  orderFilters: OrderFilters;
  productFilters: ProductFilters;
  customerFilters: CustomerFilters;
  
  // Saved filters
  savedFilters: SavedFilter[];
  
  // Actions
  setOrderFilters: (filters: Partial<OrderFilters>) => void;
  resetOrderFilters: () => void;
  
  setProductFilters: (filters: Partial<ProductFilters>) => void;
  resetProductFilters: () => void;
  
  setCustomerFilters: (filters: Partial<CustomerFilters>) => void;
  resetCustomerFilters: () => void;
  
  saveFilter: (name: string, type: 'orders' | 'products' | 'customers', filters: Record<string, any>) => void;
  loadFilter: (id: string) => void;
  deleteFilter: (id: string) => void;
}

const defaultOrderFilters: OrderFilters = {
  page: 1,
  page_size: 20,
  ordering: '-created_at'
};

const defaultProductFilters: ProductFilters = {
  page: 1,
  page_size: 20,
  ordering: 'name'
};

const defaultCustomerFilters: CustomerFilters = {
  page: 1,
  page_size: 20,
  ordering: '-date_joined'
};

export const useFilterStore = create<FilterState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        orderFilters: defaultOrderFilters,
        productFilters: defaultProductFilters,
        customerFilters: defaultCustomerFilters,
        savedFilters: [],
        
        // Order filter actions
        setOrderFilters: (filters) => set((state) => ({
          orderFilters: { ...state.orderFilters, ...filters }
        })),
        
        resetOrderFilters: () => set({ orderFilters: defaultOrderFilters }),
        
        // Product filter actions
        setProductFilters: (filters) => set((state) => ({
          productFilters: { ...state.productFilters, ...filters }
        })),
        
        resetProductFilters: () => set({ productFilters: defaultProductFilters }),
        
        // Customer filter actions
        setCustomerFilters: (filters) => set((state) => ({
          customerFilters: { ...state.customerFilters, ...filters }
        })),
        
        resetCustomerFilters: () => set({ customerFilters: defaultCustomerFilters }),
        
        // Saved filter actions
        saveFilter: (name, type, filters) => {
          const id = Math.random().toString(36).substr(2, 9);
          const savedFilter: SavedFilter = {
            id,
            name,
            type,
            filters,
            created_at: new Date().toISOString()
          };
          
          set((state) => ({
            savedFilters: [...state.savedFilters, savedFilter]
          }));
        },
        
        loadFilter: (id) => {
          const { savedFilters } = get();
          const filter = savedFilters.find(f => f.id === id);
          
          if (filter) {
            switch (filter.type) {
              case 'orders':
                set({ orderFilters: { ...defaultOrderFilters, ...filter.filters } });
                break;
              case 'products':
                set({ productFilters: { ...defaultProductFilters, ...filter.filters } });
                break;
              case 'customers':
                set({ customerFilters: { ...defaultCustomerFilters, ...filter.filters } });
                break;
            }
          }
        },
        
        deleteFilter: (id) => set((state) => ({
          savedFilters: state.savedFilters.filter(f => f.id !== id)
        }))
      }),
      {
        name: 'admin-filter-store',
        partialize: (state) => ({
          savedFilters: state.savedFilters,
          orderFilters: state.orderFilters,
          productFilters: state.productFilters,
          customerFilters: state.customerFilters
        })
      }
    ),
    { name: 'FilterStore' }
  )
);
```

### 4. Selection Store for Bulk Operations

```typescript
// src/stores/selection-store.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface BulkOperation {
  id: string;
  type: 'orders' | 'products' | 'customers';
  action: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  total: number;
  completed: number;
  failed: number;
  started_at: string;
  completed_at?: string;
  error?: string;
}

interface SelectionState {
  // Selected items
  selectedOrders: Set<number>;
  selectedProducts: Set<number>;
  selectedCustomers: Set<number>;
  
  // Bulk operations
  bulkOperations: BulkOperation[];
  
  // Actions
  selectOrder: (id: number) => void;
  deselectOrder: (id: number) => void;
  selectAllOrders: (ids: number[]) => void;
  clearOrderSelection: () => void;
  
  selectProduct: (id: number) => void;
  deselectProduct: (id: number) => void;
  selectAllProducts: (ids: number[]) => void;
  clearProductSelection: () => void;
  
  selectCustomer: (id: number) => void;
  deselectCustomer: (id: number) => void;
  selectAllCustomers: (ids: number[]) => void;
  clearCustomerSelection: () => void;
  
  addBulkOperation: (operation: Omit<BulkOperation, 'id'>) => string;
  updateBulkOperation: (id: string, updates: Partial<BulkOperation>) => void;
  removeBulkOperation: (id: string) => void;
  clearCompletedOperations: () => void;
}

export const useSelectionStore = create<SelectionState>()(
  devtools(
    (set, get) => ({
      // Initial state
      selectedOrders: new Set(),
      selectedProducts: new Set(),
      selectedCustomers: new Set(),
      bulkOperations: [],
      
      // Order selection actions
      selectOrder: (id) => set((state) => ({
        selectedOrders: new Set([...state.selectedOrders, id])
      })),
      
      deselectOrder: (id) => set((state) => {
        const newSelection = new Set(state.selectedOrders);
        newSelection.delete(id);
        return { selectedOrders: newSelection };
      }),
      
      selectAllOrders: (ids) => set({ selectedOrders: new Set(ids) }),
      
      clearOrderSelection: () => set({ selectedOrders: new Set() }),
      
      // Product selection actions
      selectProduct: (id) => set((state) => ({
        selectedProducts: new Set([...state.selectedProducts, id])
      })),
      
      deselectProduct: (id) => set((state) => {
        const newSelection = new Set(state.selectedProducts);
        newSelection.delete(id);
        return { selectedProducts: newSelection };
      }),
      
      selectAllProducts: (ids) => set({ selectedProducts: new Set(ids) }),
      
      clearProductSelection: () => set({ selectedProducts: new Set() }),
      
      // Customer selection actions
      selectCustomer: (id) => set((state) => ({
        selectedCustomers: new Set([...state.selectedCustomers, id])
      })),
      
      deselectCustomer: (id) => set((state) => {
        const newSelection = new Set(state.selectedCustomers);
        newSelection.delete(id);
        return { selectedCustomers: newSelection };
      }),
      
      selectAllCustomers: (ids) => set({ selectedCustomers: new Set(ids) }),
      
      clearCustomerSelection: () => set({ selectedCustomers: new Set() }),
      
      // Bulk operation actions
      addBulkOperation: (operation) => {
        const id = Math.random().toString(36).substr(2, 9);
        const newOperation = { ...operation, id };
        
        set((state) => ({
          bulkOperations: [...state.bulkOperations, newOperation]
        }));
        
        return id;
      },
      
      updateBulkOperation: (id, updates) => set((state) => ({
        bulkOperations: state.bulkOperations.map(op =>
          op.id === id ? { ...op, ...updates } : op
        )
      })),
      
      removeBulkOperation: (id) => set((state) => ({
        bulkOperations: state.bulkOperations.filter(op => op.id !== id)
      })),
      
      clearCompletedOperations: () => set((state) => ({
        bulkOperations: state.bulkOperations.filter(op => 
          op.status !== 'completed' && op.status !== 'failed'
        )
      }))
    }),
    { name: 'SelectionStore' }
  )
);
```

## Store Integration Hooks

### 1. Notification Hook

```typescript
// src/hooks/use-notifications.ts
import { useUIStore } from '../stores/ui-store';

export const useNotifications = () => {
  const { addNotification, removeNotification, clearNotifications, notifications } = useUIStore();
  
  const showSuccess = (title: string, message?: string) => {
    addNotification({ type: 'success', title, message });
  };
  
  const showError = (title: string, message?: string) => {
    addNotification({ type: 'error', title, message, duration: 0 });
  };
  
  const showWarning = (title: string, message?: string) => {
    addNotification({ type: 'warning', title, message });
  };
  
  const showInfo = (title: string, message?: string) => {
    addNotification({ type: 'info', title, message });
  };
  
  return {
    notifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    removeNotification,
    clearNotifications
  };
};
```

### 2. Bulk Operations Hook

```typescript
// src/hooks/use-bulk-operations.ts
import { useSelectionStore } from '../stores/selection-store';
import { useNotifications } from './use-notifications';

export const useBulkOperations = () => {
  const { 
    bulkOperations, 
    addBulkOperation, 
    updateBulkOperation, 
    removeBulkOperation 
  } = useSelectionStore();
  const { showSuccess, showError } = useNotifications();
  
  const startBulkOperation = async (
    type: 'orders' | 'products' | 'customers',
    action: string,
    items: number[],
    operationFn: (items: number[]) => Promise<void>
  ) => {
    const operationId = addBulkOperation({
      type,
      action,
      status: 'pending',
      progress: 0,
      total: items.length,
      completed: 0,
      failed: 0,
      started_at: new Date().toISOString()
    });
    
    try {
      updateBulkOperation(operationId, { status: 'running' });
      
      await operationFn(items);
      
      updateBulkOperation(operationId, {
        status: 'completed',
        progress: 100,
        completed: items.length,
        completed_at: new Date().toISOString()
      });
      
      showSuccess(`Bulk ${action} completed`, `Successfully processed ${items.length} items`);
    } catch (error) {
      updateBulkOperation(operationId, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        completed_at: new Date().toISOString()
      });
      
      showError(`Bulk ${action} failed`, error instanceof Error ? error.message : 'Unknown error');
    }
  };
  
  return {
    bulkOperations,
    startBulkOperation,
    updateBulkOperation,
    removeBulkOperation
  };
};
```

## State Persistence Strategy

### 1. Selective Persistence

```typescript
// Only persist essential state that should survive page refreshes
const persistedStores = {
  auth: ['isAuthenticated', 'user', 'permissions', 'groups'],
  filters: ['savedFilters', 'orderFilters', 'productFilters', 'customerFilters'],
  ui: ['theme', 'sidebarCollapsed'] // Don't persist notifications or modals
};
```

### 2. Storage Configuration

```typescript
// src/lib/storage-config.ts
export const storageConfig = {
  // Use localStorage for long-term persistence
  localStorage: {
    stores: ['auth', 'filters'],
    prefix: 'admin-arena-'
  },
  
  // Use sessionStorage for temporary state
  sessionStorage: {
    stores: ['selections'],
    prefix: 'admin-arena-session-'
  }
};
```

## Next Steps

1. **Store Implementation**: Create all Zustand stores with proper TypeScript types
2. **Integration Testing**: Test store interactions and state synchronization
3. **Performance Optimization**: Implement store selectors and memoization
4. **DevTools Integration**: Set up Redux DevTools for debugging
5. **State Migration**: Handle store schema changes and migrations
