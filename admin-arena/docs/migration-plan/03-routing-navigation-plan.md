# Routing & Navigation Technical Plan

## Overview

This document outlines the implementation plan for routing and navigation using TanStack Router's file-based routing system, with role-based access control integration.

## Package Versions

```json
{
  "@tanstack/react-router": "^1.121.34",
  "@tanstack/react-router-devtools": "^1.121.34",
  "@tanstack/router-plugin": "^1.121.34",
  "react-icons": "^5.5.0"
}
```

## File-Based Routing Structure

### 1. Route Tree Structure

```
src/routes/
├── __root.tsx                 # Root layout with auth provider
├── index.tsx                  # Dashboard home page
├── login.tsx                  # Login page (public)
├── unauthorized.tsx           # Unauthorized access page
├── orders/
│   ├── index.tsx             # Orders list page
│   ├── $orderId.tsx          # Order detail page
│   ├── my-assignments.tsx    # My assigned orders
│   └── bulk-operations.tsx   # Bulk operations page
├── products/
│   ├── index.tsx             # Products list page
│   ├── $productId.tsx        # Product detail page
│   ├── create.tsx            # Create product page
│   ├── categories/
│   │   ├── index.tsx         # Categories management
│   │   └── $categoryId.tsx   # Category detail
│   ├── brands/
│   │   ├── index.tsx         # Brands management
│   │   └── $brandId.tsx      # Brand detail
│   └── attributes/
│       ├── index.tsx         # Attributes management
│       └── $attributeId.tsx  # Attribute detail
├── customers/
│   ├── index.tsx             # Customers list page
│   ├── $customerId.tsx       # Customer detail page
│   └── analytics.tsx         # Customer analytics
├── staff/
│   ├── index.tsx             # Staff management
│   ├── $staffId.tsx          # Staff profile detail
│   ├── roles/
│   │   ├── index.tsx         # Roles management
│   │   └── $roleId.tsx       # Role detail
│   └── permissions.tsx       # Permissions management
├── analytics/
│   ├── index.tsx             # Analytics dashboard
│   ├── sales.tsx             # Sales analytics
│   └── performance.tsx       # Performance metrics
└── settings/
    ├── index.tsx             # General settings
    ├── profile.tsx           # User profile settings
    └── system.tsx            # System settings
```

### 2. Root Route Configuration

```typescript
// src/routes/__root.tsx
import { createRootRoute, Outlet } from '@tanstack/react-router';
import { TanStackRouterDevtools } from '@tanstack/router-devtools';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import { AdminLayout } from '../components/layouts/AdminLayout';
import { AuthProvider } from '../components/auth/AuthProvider';
import { queryClient } from '../lib/query-client';

export const Route = createRootRoute({
  component: RootComponent,
});

function RootComponent() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <AdminLayout>
          <Outlet />
        </AdminLayout>
        <Toaster position="top-right" />
        <ReactQueryDevtools initialIsOpen={false} />
        <TanStackRouterDevtools />
      </AuthProvider>
    </QueryClientProvider>
  );
}
```

### 3. Protected Route Examples

```typescript
// src/routes/orders/index.tsx
import { createFileRoute } from '@tanstack/react-router';
import { z } from 'zod';
import { OrdersListPage } from '../../pages/orders/OrdersListPage';
import { useAuthGuard } from '../../hooks/use-auth-guard';

const ordersSearchSchema = z.object({
  page: z.number().optional().default(1),
  status: z.string().optional(),
  assigned_to: z.number().optional(),
  search: z.string().optional(),
});

export const Route = createFileRoute('/orders/')({
  validateSearch: ordersSearchSchema,
  component: OrdersComponent,
});

function OrdersComponent() {
  const { isAuthenticated, hasPermission } = useAuthGuard({
    permission: 'staff.view_orderproxy'
  });
  
  const search = Route.useSearch();
  
  if (!isAuthenticated || !hasPermission) {
    return <div>Loading...</div>;
  }
  
  return <OrdersListPage filters={search} />;
}
```

```typescript
// src/routes/orders/$orderId.tsx
import { createFileRoute } from '@tanstack/react-router';
import { OrderDetailPage } from '../../pages/orders/OrderDetailPage';
import { useAuthGuard } from '../../hooks/use-auth-guard';

export const Route = createFileRoute('/orders/$orderId')({
  component: OrderDetailComponent,
});

function OrderDetailComponent() {
  const { orderId } = Route.useParams();
  const { isAuthenticated, hasPermission } = useAuthGuard({
    permission: 'staff.view_orderproxy'
  });
  
  if (!isAuthenticated || !hasPermission) {
    return <div>Loading...</div>;
  }
  
  return <OrderDetailPage orderId={parseInt(orderId)} />;
}
```

### 4. Public Routes

```typescript
// src/routes/login.tsx
import { createFileRoute, redirect } from '@tanstack/react-router';
import { LoginPage } from '../pages/auth/LoginPage';
import { useAuthStore } from '../stores/auth-store';

export const Route = createFileRoute('/login')({
  beforeLoad: ({ context }) => {
    // Redirect to dashboard if already authenticated
    if (context.auth.isAuthenticated) {
      throw redirect({ to: '/' });
    }
  },
  component: LoginPage,
});
```

## Navigation System

### 1. Sidebar Navigation Configuration

```typescript
// src/config/navigation.ts
import { 
  FiHome, 
  FiShoppingCart, 
  FiPackage, 
  FiUsers, 
  FiUser, 
  FiBarChart3, 
  FiSettings 
} from 'react-icons/fi';

export interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ComponentType;
  permission?: string;
  group?: string;
  children?: NavigationItem[];
}

export const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: '/',
    icon: FiHome,
    permission: 'staff.view_dashboard',
  },
  {
    id: 'orders',
    label: 'Orders',
    path: '/orders',
    icon: FiShoppingCart,
    permission: 'staff.view_orderproxy',
    children: [
      {
        id: 'orders-all',
        label: 'All Orders',
        path: '/orders',
        icon: FiShoppingCart,
        permission: 'staff.view_orderproxy',
      },
      {
        id: 'orders-my-assignments',
        label: 'My Assignments',
        path: '/orders/my-assignments',
        icon: FiUser,
        permission: 'staff.view_orderproxy',
      },
      {
        id: 'orders-bulk-operations',
        label: 'Bulk Operations',
        path: '/orders/bulk-operations',
        icon: FiPackage,
        permission: 'staff.bulk_order_operations',
      },
    ],
  },
  {
    id: 'products',
    label: 'Products',
    path: '/products',
    icon: FiPackage,
    permission: 'staff.view_productproxy',
    children: [
      {
        id: 'products-all',
        label: 'All Products',
        path: '/products',
        icon: FiPackage,
        permission: 'staff.view_productproxy',
      },
      {
        id: 'products-categories',
        label: 'Categories',
        path: '/products/categories',
        icon: FiPackage,
        permission: 'staff.view_categoryproxy',
      },
      {
        id: 'products-brands',
        label: 'Brands',
        path: '/products/brands',
        icon: FiPackage,
        permission: 'staff.view_brandproxy',
      },
      {
        id: 'products-attributes',
        label: 'Attributes',
        path: '/products/attributes',
        icon: FiPackage,
        permission: 'staff.view_attributeproxy',
      },
    ],
  },
  {
    id: 'customers',
    label: 'Customers',
    path: '/customers',
    icon: FiUsers,
    permission: 'staff.view_customerproxy',
    children: [
      {
        id: 'customers-all',
        label: 'All Customers',
        path: '/customers',
        icon: FiUsers,
        permission: 'staff.view_customerproxy',
      },
      {
        id: 'customers-analytics',
        label: 'Analytics',
        path: '/customers/analytics',
        icon: FiBarChart3,
        permission: 'staff.customer_analytics',
      },
    ],
  },
  {
    id: 'staff',
    label: 'Staff Management',
    path: '/staff',
    icon: FiUser,
    group: 'Staff Manager (SM)',
    children: [
      {
        id: 'staff-all',
        label: 'Staff Members',
        path: '/staff',
        icon: FiUser,
        permission: 'staff.view_staffprofile',
      },
      {
        id: 'staff-roles',
        label: 'Roles & Permissions',
        path: '/staff/roles',
        icon: FiSettings,
        permission: 'auth.view_group',
      },
    ],
  },
  {
    id: 'analytics',
    label: 'Analytics',
    path: '/analytics',
    icon: FiBarChart3,
    permission: 'staff.view_analytics',
    children: [
      {
        id: 'analytics-dashboard',
        label: 'Overview',
        path: '/analytics',
        icon: FiBarChart3,
        permission: 'staff.view_analytics',
      },
      {
        id: 'analytics-sales',
        label: 'Sales Analytics',
        path: '/analytics/sales',
        icon: FiBarChart3,
        permission: 'staff.view_sales_analytics',
      },
      {
        id: 'analytics-performance',
        label: 'Performance',
        path: '/analytics/performance',
        icon: FiBarChart3,
        permission: 'staff.view_performance_analytics',
      },
    ],
  },
  {
    id: 'settings',
    label: 'Settings',
    path: '/settings',
    icon: FiSettings,
    children: [
      {
        id: 'settings-profile',
        label: 'Profile',
        path: '/settings/profile',
        icon: FiUser,
      },
      {
        id: 'settings-system',
        label: 'System',
        path: '/settings/system',
        icon: FiSettings,
        permission: 'staff.system_settings',
      },
    ],
  },
];
```

### 2. Sidebar Component

```typescript
// src/components/navigation/Sidebar.tsx
import React, { useState } from 'react';
import { Link, useLocation } from '@tanstack/react-router';
import { FiChevronDown, FiChevronRight } from 'react-icons/fi';
import { useAuthStore } from '../../stores/auth-store';
import { navigationItems, NavigationItem } from '../../config/navigation';
import { PermissionGuard } from '../auth/PermissionGuard';
import styles from './Sidebar.module.scss';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, onToggle }) => {
  const location = useLocation();
  const { user, hasGroup } = useAuthStore();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const isItemVisible = (item: NavigationItem): boolean => {
    // Superuser can see everything
    if (user?.is_superuser) return true;
    
    // Check group membership
    if (item.group && !hasGroup(item.group)) return false;
    
    // Check permission (handled by PermissionGuard)
    return true;
  };

  const isItemActive = (path: string): boolean => {
    return location.pathname === path;
  };

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    if (!isItemVisible(item)) return null;

    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);
    const isActive = isItemActive(item.path);

    return (
      <PermissionGuard key={item.id} permission={item.permission} group={item.group}>
        <div className={styles.navigationItem}>
          <div
            className={`${styles.itemContent} ${isActive ? styles.active : ''}`}
            style={{ paddingLeft: `${level * 16 + 16}px` }}
          >
            {hasChildren ? (
              <button
                className={styles.expandButton}
                onClick={() => toggleExpanded(item.id)}
                aria-expanded={isExpanded}
              >
                <item.icon className={styles.icon} />
                {!isCollapsed && (
                  <>
                    <span className={styles.label}>{item.label}</span>
                    {isExpanded ? (
                      <FiChevronDown className={styles.chevron} />
                    ) : (
                      <FiChevronRight className={styles.chevron} />
                    )}
                  </>
                )}
              </button>
            ) : (
              <Link
                to={item.path}
                className={styles.link}
                activeProps={{ className: styles.activeLink }}
              >
                <item.icon className={styles.icon} />
                {!isCollapsed && <span className={styles.label}>{item.label}</span>}
              </Link>
            )}
          </div>

          {hasChildren && isExpanded && !isCollapsed && (
            <div className={styles.submenu}>
              {item.children?.map((child) => renderNavigationItem(child, level + 1))}
            </div>
          )}
        </div>
      </PermissionGuard>
    );
  };

  return (
    <aside className={`${styles.sidebar} ${isCollapsed ? styles.collapsed : ''}`}>
      <div className={styles.header}>
        <h2 className={styles.title}>
          {isCollapsed ? 'AA' : 'Admin Arena'}
        </h2>
        <button className={styles.toggleButton} onClick={onToggle}>
          {isCollapsed ? '→' : '←'}
        </button>
      </div>

      <nav className={styles.navigation}>
        {navigationItems.map((item) => renderNavigationItem(item))}
      </nav>
    </aside>
  );
};
```

### 3. Breadcrumb Component

```typescript
// src/components/navigation/Breadcrumb.tsx
import React from 'react';
import { Link, useMatches } from '@tanstack/react-router';
import { FiChevronRight, FiHome } from 'react-icons/fi';
import styles from './Breadcrumb.module.scss';

interface BreadcrumbItem {
  label: string;
  path: string;
  isActive: boolean;
}

export const Breadcrumb: React.FC = () => {
  const matches = useMatches();

  const breadcrumbItems: BreadcrumbItem[] = React.useMemo(() => {
    const items: BreadcrumbItem[] = [
      { label: 'Dashboard', path: '/', isActive: false }
    ];

    matches.forEach((match, index) => {
      if (match.pathname === '/') return;

      const segments = match.pathname.split('/').filter(Boolean);
      const isLast = index === matches.length - 1;

      // Generate breadcrumb based on route segments
      segments.forEach((segment, segmentIndex) => {
        const path = '/' + segments.slice(0, segmentIndex + 1).join('/');
        const label = segment.charAt(0).toUpperCase() + segment.slice(1);
        
        items.push({
          label: label.replace('-', ' '),
          path,
          isActive: isLast && segmentIndex === segments.length - 1
        });
      });
    });

    return items;
  }, [matches]);

  return (
    <nav className={styles.breadcrumb} aria-label="Breadcrumb">
      <ol className={styles.list}>
        {breadcrumbItems.map((item, index) => (
          <li key={item.path} className={styles.item}>
            {index === 0 && <FiHome className={styles.homeIcon} />}
            
            {item.isActive ? (
              <span className={styles.activeItem}>{item.label}</span>
            ) : (
              <Link to={item.path} className={styles.link}>
                {item.label}
              </Link>
            )}
            
            {index < breadcrumbItems.length - 1 && (
              <FiChevronRight className={styles.separator} />
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};
```

### 4. Route Context for Authentication

```typescript
// src/lib/router-context.ts
import { createContext } from 'react';
import { useAuthStore } from '../stores/auth-store';

export interface RouterContext {
  auth: {
    isAuthenticated: boolean;
    user: StaffUser | null;
    checkPermission: (permission: string) => boolean;
    hasGroup: (group: string) => boolean;
  };
}

export const createRouterContext = (): RouterContext => {
  const { isAuthenticated, user, checkPermission, hasGroup } = useAuthStore.getState();
  
  return {
    auth: {
      isAuthenticated,
      user,
      checkPermission,
      hasGroup,
    },
  };
};
```

### 5. Router Configuration

```typescript
// src/main.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { RouterProvider, createRouter } from '@tanstack/react-router';
import { routeTree } from './routeTree.gen';
import { createRouterContext } from './lib/router-context';
import './scss/index.scss';

const router = createRouter({
  routeTree,
  context: createRouterContext(),
});

declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>
);
```

## Route Guards and Middleware

### 1. Route-Level Guards

```typescript
// src/hooks/use-route-guard.ts
import { useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { useAuthStore } from '../stores/auth-store';

interface RouteGuardOptions {
  requireAuth?: boolean;
  permission?: string;
  group?: string;
  redirectTo?: string;
}

export const useRouteGuard = (options: RouteGuardOptions = {}) => {
  const {
    requireAuth = true,
    permission,
    group,
    redirectTo = '/login'
  } = options;
  
  const { isAuthenticated, checkPermission, hasGroup, user } = useAuthStore();
  const navigate = useNavigate();

  useEffect(() => {
    if (requireAuth && !isAuthenticated) {
      navigate({ to: redirectTo });
      return;
    }

    if (user?.is_superuser) {
      return; // Superuser bypasses all checks
    }

    if (permission && !checkPermission(permission)) {
      navigate({ to: '/unauthorized' });
      return;
    }

    if (group && !hasGroup(group)) {
      navigate({ to: '/unauthorized' });
      return;
    }
  }, [isAuthenticated, permission, group, checkPermission, hasGroup, user, navigate, redirectTo, requireAuth]);

  return {
    isAuthenticated,
    hasPermission: permission ? checkPermission(permission) : true,
    hasGroup: group ? hasGroup(group) : true,
    user
  };
};
```

## Next Steps

1. **Route Generation**: Set up TanStack Router plugin for automatic route generation
2. **Navigation Styling**: Implement SCSS modules for sidebar and breadcrumb components
3. **Route Transitions**: Add loading states and page transitions
4. **Deep Linking**: Implement proper URL state management for filters and pagination
5. **Testing**: Write tests for navigation components and route guards
