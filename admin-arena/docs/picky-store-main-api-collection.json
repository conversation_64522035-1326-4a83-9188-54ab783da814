{"info": {"_postman_id": "e3290e33-8217-4c50-ab99-c5aa1bd3ecd3", "name": "picky-store", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "19599306"}, "item": [{"name": "Staff Operations", "item": [{"name": "Authentication", "item": [{"name": "Registration", "item": [{"name": "1-Initiate Registration", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "// {\r\n//     \"phone_number\": \"+47469 39 603\"\r\n// }\r\n\r\n{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/auth/initiate-registration/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["auth", "initiate-registration", ""]}}, "response": []}, {"name": "2-Verify Reg Credentials", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "// {\r\n//     \"phone_number\": \"+94783692722\",\r\n//     \"code\": \"300340\"\r\n// }\r\n\r\n{\r\n    \"email\": \"<EMAIL>\", \r\n    \"code\": \"369101\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/auth/verify-code/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["auth", "verify-code", ""]}}, "response": []}, {"name": "3-Set Password", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"password\": \"ILove-Django1\", \r\n    \"confirm_password\": \"ILove-Django1\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/auth/set-password/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["auth", "set-password", ""]}}, "response": []}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"<EMAIL>\",\r\n    \"password\": \"1234\"\r\n}\r\n\r\n// {\r\n//     \"username\": \"ve<PERSON><PERSON><PERSON>@post.no\",\r\n//     \"password\": \"ILove-Django1\"\r\n// }\r\n\r\n// {\r\n//     \"username\": \"<EMAIL>\",\r\n//     \"password\": \"ILove-Django1\"\r\n// }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/auth/users/login/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["auth", "users", "login", ""]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:8000/auth/users/logout/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["auth", "users", "logout", ""]}}, "response": []}, {"name": "User Info", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/auth/user/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "auth", "user", ""]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "Get a List of Users", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/users/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "users", ""]}}, "response": []}, {"name": "Get a User by ID", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/users/2/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "users", "2", ""]}}, "response": []}]}, {"name": "Staff Management", "item": [{"name": "List Staff Users", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"permission_codename\": \"view_product\"\r\n}"}, "url": {"raw": "http://127.0.0.1:8000/api/staff/staff-profiles/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "staff-profiles", ""]}}, "response": []}, {"name": "Create a Staff User", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"email\": \"<EMAIL>\",\r\n  \"phone_number\": \"string\",\r\n  \"employee_id\": \"string\",\r\n  \"department\": \"PRODUCT\",\r\n  \"position_title\": \"string\",\r\n  \"manager_id\": 0,\r\n  \"hire_date\": \"2019-08-24\",\r\n  \"notes\": \"string\",\r\n  \"role_ids\": [\r\n    0\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/staff-management/create_staff_user/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "staff-management", "create_staff_user", ""]}}, "response": []}, {"name": "Change Staff User Status", "request": {"method": "POST", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/staff-profiles/{id}/change_status/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "staff-profiles", "{id}", "change_status", ""]}}, "response": []}, {"name": "Get Staff Profile by ID", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/staff-profiles/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "staff-profiles", "{id}", ""]}}, "response": []}, {"name": "Update a Staff Profile", "request": {"method": "PUT", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/staff-profiles/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "staff-profiles", "{id}", ""]}}, "response": []}, {"name": "Partially Update Staff Profile", "request": {"method": "PATCH", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/staff-profiles/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "staff-profiles", "{id}", ""]}}, "response": []}, {"name": "Delete a Staff Profile", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/staff-profiles/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "staff-profiles", "{id}", ""]}}, "response": []}, {"name": "Get a Staff Report", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/staff-profiles/{id}/direct_reports/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "staff-profiles", "{id}", "direct_reports", ""]}}, "response": []}, {"name": "Get Users by Department", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/staff-profiles/department_summary/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "staff-profiles", "department_summary", ""]}}, "response": []}, {"name": "Change Staff User Status", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"user\": 0,\r\n  \"employee_id\": \"string\",\r\n  \"department\": \"PRODUCT\",\r\n  \"position_title\": \"string\",\r\n  \"manager\": 0,\r\n  \"hire_date\": \"2019-08-24\",\r\n  \"status\": \"ACTIVE\",\r\n  \"notes\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/staff-profiles/{id}/change_status/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "staff-profiles", "{id}", "change_status", ""]}}, "response": []}]}, {"name": "Managing Roles (Groups)", "item": [{"name": "Managing Role (Group) Members", "item": [{"name": "Get Role Members", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": 3,\r\n}"}, "url": {"raw": "http://127.0.0.1:8000/api/staff/groups/2/add_member/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "groups", "2", "add_member", ""]}}, "response": []}]}, {"name": "Get List of Roles", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/roles/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "roles", ""]}}, "response": []}, {"name": "Create a Roles", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"CEO\",\r\n  \"permission_ids\": [0]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/roles/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "roles", ""]}}, "response": []}, {"name": "Get the Role by ID", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/roles/4/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "roles", "4", ""]}}, "response": []}, {"name": "Update a Role", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"PMEW\",\r\n  \"permission_ids\": [0]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/roles/2/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "roles", "2", ""]}}, "response": []}, {"name": "Partially Update a Role", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"PME\",\r\n  \"permission_ids\": [0]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/roles/2/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "roles", "2", ""]}}, "response": []}, {"name": "Delete a Role", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/roles/8/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "roles", "8", ""]}}, "response": []}, {"name": "Get Role Permissions", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/roles/4/permissions/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "roles", "4", "permissions", ""]}}, "response": []}, {"name": "Add a Permission to a Role", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"permission_codename\": \"view_product\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/roles/4/add_permission/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "roles", "4", "add_permission", ""]}}, "response": []}, {"name": "Remove Role Permissions", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"permission_codename\": \"view_product\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/roles/4/remove_permission/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "roles", "4", "remove_permission", ""]}}, "response": []}]}, {"name": "Permissions", "item": [{"name": "List of Permissions", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/permissions/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "permissions", ""]}}, "response": []}, {"name": "Auth User's Permission", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/auth/permissions/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "auth", "permissions", ""]}}, "response": []}]}, {"name": "Product Management", "item": [{"name": "1-Category Mgt", "item": [{"name": "List Categories", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/categories/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "categories", ""]}}, "response": []}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"HDD\",\n  \"slug\": \"hdd\",\n  \"is_active\": true,\n  \"parent\": 12\n}"}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/categories/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "categories", ""]}}, "response": []}, {"name": "Move Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"new_parent_id\": 2\n}"}, "url": {"raw": "{{base_url}}/categories/1/move/", "host": ["{{base_url}}"], "path": ["categories", "1", "move", ""]}}, "response": []}]}, {"name": "2-Product Type Mgt", "item": [{"name": "List of Product Types", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/product-types/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "product-types", ""]}}, "response": []}, {"name": "Create a Product Type", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"mouse\"\r\n//   \"parent\": 0\r\n// hard-drive\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/product-types/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "product-types", ""]}}, "response": []}, {"name": "Get a Product Type by ID", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/product-types/6/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "product-types", "6", ""]}}, "response": []}, {"name": "Update a Product Type", "request": {"method": "PUT", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/product-types/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "product-types", "{id}", ""]}}, "response": []}, {"name": "Partially Update Product Type", "request": {"method": "PATCH", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/product-types/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "product-types", "{id}", ""]}}, "response": []}, {"name": "Delete a Product Type", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/product-types/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "product-types", "{id}", ""]}}, "response": []}]}, {"name": "3-<PERSON> Mgt", "item": [{"name": "List of Brands", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brands/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brands", ""]}}, "response": []}, {"name": "Create a Brand", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"<PERSON>\",\r\n  \"slug\": \"dell\",\r\n  \"is_active\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brands/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brands", ""]}}, "response": []}, {"name": "Get a Brand by ID", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brands/5/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brands", "5", ""]}}, "response": []}, {"name": "Update a Brand", "request": {"method": "PUT", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brands/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brands", "{id}", ""]}}, "response": []}, {"name": "Partially Update a Brand", "request": {"method": "PATCH", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brands/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brands", "{id}", ""]}}, "response": []}, {"name": "Delete a Brand", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brands/6/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brands", "6", ""]}}, "response": []}]}, {"name": "4-Brand Product Type", "item": [{"name": "Associate Brand with Product Type", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "\r\n  {\r\n    \"brand\": 5,\r\n    \"product_type\": 7\r\n  }\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brand-product-types/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brand-product-types", ""]}}, "response": []}, {"name": "List Brand Product Types", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brand-product-types/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brand-product-types", ""]}}, "response": []}, {"name": "Get Update Brand Product Type By ID", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brand-product-types/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brand-product-types", "{id}", ""]}}, "response": []}, {"name": "Update Brand Product Types", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"brand\": 0,\r\n  \"product_type\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brand-product-types/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brand-product-types", "{id}", ""]}}, "response": []}, {"name": "Par. Up. Brand Product Types", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"brand\": 0,\r\n  \"product_type\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brand-product-types/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brand-product-types", "{id}", ""]}}, "response": []}, {"name": "Delete Brand Product Types", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/brand-product-types/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "brand-product-types", "{id}", ""]}}, "response": []}]}, {"name": "5-Product Attributes", "item": [{"name": "List of Attributes", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attributes/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attributes", ""]}}, "response": []}, {"name": "Get Attribute by ID", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attributes/9/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attributes", "9", ""]}}, "response": []}, {"name": "Create an Attribute", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"Connection Type\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attributes/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attributes", ""]}}, "response": []}, {"name": "Update an Attribute", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attributes/9/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attributes", "9", ""]}}, "response": []}, {"name": "Partially Update an Attribute", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attributes/9/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attributes", "9", ""]}}, "response": []}, {"name": "Delete an Attribute", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attributes/9/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attributes", "9", ""]}}, "response": []}]}, {"name": "6-Product Type Attribute", "item": [{"name": "Bulk Associate Attributes", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"attributes\": [\r\n    {\r\n      \"attribute_id\": 12,\r\n      \"is_filterable\": true,\r\n      \"is_option_selector\": false\r\n    }\r\n    // {\r\n    //   \"attribute_id\": 2,\r\n    //   \"is_filterable\": false,\r\n    //   \"is_option_selector\": true\r\n    // }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/product-types/7/associate_attributes/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "product-types", "7", "associate_attributes", ""]}}, "response": []}, {"name": "Associate a Single Attribute", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"product_type\": 1,\r\n  \"attribute\": 12,\r\n  \"is_filterable\": true,\r\n  \"is_option_selector\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/associations/save_association/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "associations", "save_association", ""]}}, "response": []}]}, {"name": "7-Attribute Value Mgt", "item": [{"name": "List Attribute Values", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attribute-values/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attribute-values", ""]}}, "response": []}, {"name": "List Att. Values by Attribute", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attribute-values/?attribute=4", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attribute-values", ""], "query": [{"key": "attribute", "value": "4"}]}}, "response": []}, {"name": "Create an Attribute Value", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"attribute\": 4,\r\n  \"attribute_value\": \"8TB\",\r\n  \"is_active\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attribute-values/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attribute-values", ""]}}, "response": []}, {"name": "Bulk Create Attribute Values", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"attribute_id\": 5,\r\n  \"values\": [\r\n    \"4,000 RPM\", \r\n    \"4,200 RPM\", \r\n    \"5,000 RPM\",\r\n    \"5,400 RPM\",\r\n    \"5,900 RPM\",\r\n    \"7,200 RPM\",\r\n    \"10,000 RPM\",\r\n    \"15,000 RPM\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attribute-values/bulk_create/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attribute-values", "bulk_create", ""]}}, "response": []}, {"name": "Update an Attribute Value", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"attribute_value\": \"Yellow\",\r\n  \"is_active\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attribute-values/<id>/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attribute-values", "<id>", ""]}}, "response": []}, {"name": "Delete an Attribute Value", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/attribute-values/<id>/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "attribute-values", "<id>", ""]}}, "response": []}]}, {"name": "8-Product Mgt", "item": [{"name": "List Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products/?page=1&page_size=20&search=laptop&is_active=true", "host": ["{{base_url}}"], "path": ["products", ""], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}, {"key": "search", "value": "laptop"}, {"key": "is_active", "value": "true"}]}}, "response": []}, {"name": "Get Product Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products/1/", "host": ["{{base_url}}"], "path": ["products", "1", ""]}}, "response": []}, {"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Western Digital 1TB 4TB 8TB WD Blue PC Internal Hard Drive\",\n  \"slug\": \"western-digital-1tb-4tb-8tb-wd-blue-pc-internal-hard-drive\",\n  \"brand\": 3,\n  \"category\": 14,\n  \"product_type\": 3,\n  \"description\": \"This reliable 3.5\\\" SATA HDD offers a massive 8TB capacity, perfect for storing games, media, and large files.\",\n  \"is_active\": true,\n  \"is_digital\": false\n}"}, "url": {"raw": "{{base_url}}/products/products/", "host": ["{{base_url}}"], "path": ["products", "products", ""]}}, "response": []}, {"name": "Update Product", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Gaming Laptop\",\n  \"description\": \"Updated description\"\n}"}, "url": {"raw": "{{base_url}}/products/products/1/", "host": ["{{base_url}}"], "path": ["products", "products", "1", ""]}}, "response": []}, {"name": "Create Product with Variants", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product\": {\n    \"title\": \"Western Digital 1TB 4TB 8TB WD Blue PC Internal Hard Drive\",\n    \"slug\": \"western-digital-1tb-4tb-8tb-wd-blue-pc-internal-hard-drive\",\n    \"brand\": 3,\n    \"category\": 14,\n    \"product_type\": 3,\n    \"description\": \"This reliable 3.5\\\" SATA HDD offers a massive 8TB capacity, perfect for storing games, media, and large files.\",\n    \"is_active\": true,\n    \"is_digital\": false\n  },\n  \"variants\": [\n    {\n      \"price\": \"1299.99\",\n      \"sku\": \"LAPTOP-PRO-16GB\",\n      \"stock_qty\": 10,\n      \"condition\": \"New\"\n    },\n    {\n      \"price\": \"1599.99\",\n      \"sku\": \"LAPTOP-PRO-32GB\",\n      \"stock_qty\": 5,\n      \"condition\": \"New\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/products/products/create_with_variants/", "host": ["{{base_url}}"], "path": ["products", "products", "create_with_variants", ""]}}, "response": []}, {"name": "Delete a Product", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/products/products/4/", "host": ["{{base_url}}"], "path": ["products", "products", "4", ""]}}, "response": []}, {"name": "Product Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products/products/analytics/", "host": ["{{base_url}}"], "path": ["products", "products", "analytics", ""]}}, "response": []}, {"name": "Bulk Operations", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation_type\": \"activate\",\n  \"product_ids\": [1, 2, 3, 4, 5]\n}"}, "url": {"raw": "{{base_url}}/products/bulk_operations/", "host": ["{{base_url}}"], "path": ["products", "bulk_operations", ""]}}, "response": []}]}, {"name": "9-Product Variants Mgt", "item": [{"name": "List of Product Variants", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/variants/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "variants", ""]}}, "response": []}, {"name": "Create a Product Variant", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "  {\r\n    \"product\": 5,\r\n    \"price\": \"42.99\",\r\n    \"price_label\": 17,\r\n    \"sku\": \"MOUSE-RED\",\r\n    \"stock_qty\": 100,\r\n    \"is_active\": true,\r\n    \"weight\": 440\r\n    // \"condition\": \"new\"\r\n  }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/variants/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "variants", ""]}}, "response": []}, {"name": "Get Product Variant by ID", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/variants/1/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "variants", "1", ""]}}, "response": []}, {"name": "Update a Product Variant", "request": {"method": "PUT", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/variants/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "variants", "{id}", ""]}}, "response": []}, {"name": "Partially Update a Pro. Variant", "request": {"method": "PATCH", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/variants/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "variants", "{id}", ""]}}, "response": []}, {"name": "Delete a Product Variant", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/variants/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "variants", "{id}", ""]}}, "response": []}]}, {"name": "11-Product Variant Attribute Value", "item": [{"name": "List Pro. Variant Att. Value Associations", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/variant-attribute-values/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "variant-attribute-values", ""]}}, "response": []}, {"name": "Asso. Att. Values to a Pro. Variant", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"product_variant_id\": 1,\r\n  \"attribute_value_ids\": [17, 29]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/variant-attribute-values/bulk_associate/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "variant-attribute-values", "bulk_associate", ""]}}, "response": []}, {"name": "Update Pro. Vari. Att. Value Asscociations", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"attribute_value\": 24\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/variant-attribute-values/<id>/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "variant-attribute-values", "<id>", ""]}}, "response": []}, {"name": "Delete Pro. Vari. Att. Value Asscociations", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/variant-attribute-values/<id>/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "variant-attribute-values", "<id>", ""]}}, "response": []}]}, {"name": "10-Product Image Mgt", "item": [{"name": "Add an image", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "alternative_text", "value": "1TB hard drive", "type": "text"}, {"key": "image", "type": "file", "src": "/C:/Users/<USER>/Desktop/1tb-wd-hard-drive.jpg"}, {"key": "product_variant", "value": "1", "type": "text"}]}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/images/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "images", ""]}}, "response": []}, {"name": "Get an image by ID", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "alternative_text", "value": "1TB hard drive", "type": "text"}, {"key": "image", "type": "file", "src": "/C:/Users/<USER>/Desktop/1tb-wd-hard-drive.jpg"}, {"key": "product_variant", "value": "1", "type": "text"}]}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/images/1/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "images", "1", ""]}}, "response": []}, {"name": "Update an image", "request": {"method": "PUT", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/images/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "images", "{id}", ""]}}, "response": []}, {"name": "Partially Update an image", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "alternative_text", "value": "1TB hard drive", "type": "text"}]}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/images/1/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "images", "1", ""]}}, "response": []}, {"name": "Delete an image", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/products/images/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "images", "{id}", ""]}}, "response": []}]}, {"name": "Audit & Operations", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/audit/?product_id=1", "host": ["{{base_url}}"], "path": ["audit", ""], "query": [{"key": "product_id", "value": "1"}]}}, "response": []}, {"name": "Bulk Operations Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/bulk-operations/", "host": ["{{base_url}}"], "path": ["bulk-operations", ""]}}, "response": []}]}, {"name": "Product Attribute Value", "item": [{"name": "List Product Associated Att. Values", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/product-attribute-values/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "product-attribute-values", ""]}}, "response": []}, {"name": "Update Pro. Att. Value Associations", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"attribute_value\": 21\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/product-attribute-values/<id>/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "product-attribute-values", "<id>", ""]}}, "response": []}, {"name": "Delete Pro. Att. Value Association", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/product-attribute-values/<id>/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "product-attribute-values", "<id>", ""]}}, "response": []}, {"name": "Associate Att. Values to a Product", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"product_id\": 101,\r\n  \"attribute_value_ids\": [21, 22, 23]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/product-attribute-values/bulk_associate/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "product-attribute-values", "bulk_associate", ""]}}, "response": []}]}, {"name": "Review Mgt", "item": [{"name": "Get a List of Reviews", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"product_variant_id\": 1,\r\n  \"attribute_value_ids\": [17, 29]\r\n}"}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/reviews/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "reviews", ""]}}, "response": []}, {"name": "Get a Review by ID", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"product_variant_id\": 1,\r\n  \"attribute_value_ids\": [17, 29]\r\n}"}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/reviews/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "reviews", "{id}", ""]}}, "response": []}, {"name": "Delete a Review", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"product_variant_id\": 1,\r\n  \"attribute_value_ids\": [17, 29]\r\n}"}, "url": {"raw": "http://127.0.0.1:8000/api/staff/products/reviews/{id}/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "products", "reviews", "{id}", ""]}}, "response": []}]}]}, {"name": "Order Management", "item": [{"name": "List of orders", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/orders/orders/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "orders", "orders", ""]}}, "response": []}, {"name": "Get an Order by ID", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/staff/orders/orders/1/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "orders", "orders", "1", ""]}}, "response": []}, {"name": "Generate Documents", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "\r\n{\r\n    \"order_ids\": [1, 2],\r\n    \"document_types\": [\"SHIPPING_LABEL\", \"WAREHOUSE_PICKUP\", \"CUSTOMER_INVOICE\"],\r\n    \"include_customer_invoice\": true,\r\n    \"include_warranty_info\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/staff/orders/orders/bulk_generate_documents/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "staff", "orders", "orders", "bulk_generate_documents", ""]}}, "response": []}]}]}], "variable": [{"key": "base_url", "value": "", "type": "default"}]}