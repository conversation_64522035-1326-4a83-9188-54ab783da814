---
description: Forms & validation rules (React Hook Form + Zod)
globs:
  - src/pages/**/*.tsx
  - src/components/**/*.tsx
alwaysApply: true
---

### Forms & validation

Do
- Use `react-hook-form` with `zod` resolvers for type-safe validation.
- Co-locate schemas with forms or in `src/types/*` if shared.
- Map server errors via `setError`.

Avoid
- Uncontrolled custom inputs without proper `Controller` bindings.

Reference
- React Hook Form `useForm` — https://react-hook-form.com/docs/useform
