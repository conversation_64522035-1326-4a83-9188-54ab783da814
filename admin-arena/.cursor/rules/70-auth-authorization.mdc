---
description: Authentication & authorization rules (cookie-based)
globs:
  - src/components/auth/**/*.tsx
  - src/stores/auth-store.ts
  - src/services/auth-service.ts
  - src/routes/**/*.tsx
alwaysApply: true
---

### Authentication & authorization

Model
- Cookie-based auth; tokens are HTTP-only.

Do
- Use `AuthService` for auth flows and `useAuthStore` selectors.
- Initialize on app load via `AuthProvider`.
- Guard routes/components with `AuthGuard`/`PermissionGuard`/`GroupGuard`.
- Render loading while `isLoading` to avoid premature redirects.

Avoid
- Persisting sensitive auth data.
- Bypassing store permissions/groups for access checks.
