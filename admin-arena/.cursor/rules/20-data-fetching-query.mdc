---
description: Server state management rules (TanStack Query)
globs:
  - src/hooks/**/*.ts*
  - src/services/**/*.ts*
  - src/lib/query-client.ts
alwaysApply: true
---

### Server state rules (TanStack Query)

Do
- Centralize client config in `src/lib/query-client.ts` only. Reuse `queryClient`/`queryUtils`.
- Define query keys via `src/services/query-keys.ts` for consistent cache ops.
- Wrap queries/mutations inside `src/hooks/*` and keep components thin.
- Scope `staleTime`/`gcTime` per entity; override per-query sparingly.
- Use Axios client from `src/services/api-client.ts` for all calls.
- Invalidate using helpers (`queryUtils`, `cacheUtils`).

Avoid
- Fetching directly in components without Query.
- Storing server state in Zustand unless necessary.
- Persisting highly dynamic queries (orders, analytics) — see existing persistence filters.

Patterns
- Hook query:
```ts
export function useProducts(filters) {
  return useQuery({
    queryKey: queryKeys.products.list(filters),
    queryFn: () => ProductService.getProducts(filters),
  })
}
```
- Mutation invalidate:
```ts
const qc = useQueryClient()
return useMutation({
  mutationFn: (data) => ProductService.createProduct(data),
  onSuccess: () => qc.invalidateQueries({ queryKey: queryKeys.products.lists() }),
})
```

References
- TanStack Query overview — https://tanstack.com/query/latest/docs/framework/react/overview
- Queries guide — https://tanstack.com/query/latest/docs/framework/react/guides/queries
