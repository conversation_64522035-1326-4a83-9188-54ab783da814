---
description: Styling rules (SCSS Modules + global SCSS)
globs:
  - src/components/**/*.tsx
  - src/pages/**/*.tsx
  - src/ui/**/*.tsx
alwaysApply: true
---

### Styling rules

Do
- Use SCSS Modules with components: `Component.module.scss`.
- Import as `styles` and use `styles.foo`.
- Centralize variables/mixins in `src/scss/variables.scss` and `_mixins.scss`.
- Keep global baseline in `src/scss/index.scss`.

Avoid
- Global class collisions; prefer module scope.
- Non-trivial inline styles.

Reference
- Vite CSS Modules — https://vite.dev/guide/features.html#css-modules
