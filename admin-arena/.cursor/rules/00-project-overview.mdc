---
description: Project overview and conventions for Admin Arena
alwaysApply: false
---
### Project overview and conventions

**Stack**
- React 19 + TypeScript + Vite 6 (SWC plugin)
- TanStack Router 1 (file-based routing via plugin; generated `routeTree.gen.ts`)
- TanStack Query 5 (React Query) with persistence
- Zustand (UI + Auth stores)
- Axios (HTTP-only cookie-based auth; `withCredentials: true`)
- SCSS Modules for component styles (+ global `src/scss/*`)
- Form: React Hook Form + Zod

**Key structure**
- `src/routes/*`: File-based routes. Do not edit `src/routeTree.gen.ts` (generated).
- `src/components/*`: Reusable UI/layout/auth components.
- `src/pages/*`: Page-level components used by routes.
- `src/services/*`: API service layer (wraps Axios via `APIClient`).
- `src/hooks/*`: Feature hooks (often wrap TanStack Query + services).
- `src/stores/*`: Zustand stores for UI/Auth.
- `src/lib/query-client.ts`: Central React Query client config and persistence.
- `src/scss/*`: Global styles, variables, resets.

**High-level conventions**
- Prefer file-based routing. Compose layouts/providers in `routes/__root.tsx`.
- Fetch server state with TanStack Query inside hooks (co-locate under `src/hooks`).
- Call backend through `src/services/*` only (typed `APIClient` wrapper).
- Keep client state in co-located component state or Zustand when shared/cross-cutting.
- Use SCSS Modules per component; keep globals limited to `src/scss`.
- Environment: only expose `VITE_*` vars; use `VITE_API_BASE_URL` for Axios base.

**Security/auth**
- Pure cookie-based authentication; no token storage in frontend. Axios is configured with `withCredentials: true` and refresh handled server-side. On 401, client attempts refresh once, then redirects to `/login`.

References (from MCP web fetch)
- TanStack Router overview — https://tanstack.com/router/latest/docs/framework/react/overview
- TanStack Query overview — https://tanstack.com/query/latest/docs/framework/react/overview
- Queries guide — https://tanstack.com/query/latest/docs/framework/react/guides/queries
- Zustand intro — https://docs.pmnd.rs/zustand/getting-started/introduction
- Vite env & modes — https://vite.dev/guide/env-and-mode
- Axios config defaults — https://axios-http.com/docs/config_defaults
