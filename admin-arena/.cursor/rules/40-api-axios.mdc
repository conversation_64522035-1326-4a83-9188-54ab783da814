---
description: API/HTTP rules (Axios + cookie-based auth)
globs:
  - src/services/**/*.ts
  - src/hooks/**/*.ts*
alwaysApply: true
---

### API/HTTP rules (Axios + cookie auth)

Do
- Use `src/services/api-client.ts` (`apiClient`, `APIClient<T>`) only.
- Keep `withCredentials: true`; backend manages HTTP-only cookies.
- Handle 401 via built-in interceptor (single refresh) then redirect to `/login`.
- Implement domain services in `src/services/*` with explicit types.
- Map Axios errors to friendly messages at service boundary.

Avoid
- Managing tokens in frontend.
- Duplicating baseURL; rely on `VITE_API_BASE_URL`.

Environment
- Define `VITE_API_BASE_URL` in `.env` files.

References
- Axios config defaults — https://axios-http.com/docs/config_defaults
- Vite env & modes — https://vite.dev/guide/env-and-mode
