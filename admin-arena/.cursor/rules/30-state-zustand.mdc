---
description: Client state rules (Zustand)
globs:
  - src/stores/**/*.ts
  - src/components/**/*.tsx
alwaysApply: true
---

### Client state rules (Zustand)

Do
- Use stores for cross-cutting UI/auth state: `src/stores/ui-store.ts`, `src/stores/auth-store.ts`.
- Create selector hooks to minimize re-renders.
- Keep store actions side-effect free except for explicit async actions calling services.
- Persist only non-sensitive UI state with `partialize`.

Avoid
- Long-term server state snapshots in Zustand; prefer Query.
- Persisting auth-sensitive data.

Pattern
```ts
const toggleSidebar = useUIStore((s) => s.toggleSidebar)
const sidebarCollapsed = useUIStore((s) => s.sidebarCollapsed)
```

Reference
- Zustand introduction — https://docs.pmnd.rs/zustand/getting-started/introduction
